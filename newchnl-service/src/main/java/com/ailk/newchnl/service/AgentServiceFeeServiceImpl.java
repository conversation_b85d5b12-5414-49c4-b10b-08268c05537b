package com.ailk.newchnl.service;

import com.ailk.newchnl.constant.ChannelConstants;
import com.ailk.newchnl.dao.*;
import com.ailk.newchnl.entity.*;
import com.ailk.newchnl.entity.report.AdjustFeeDtl;
import com.ailk.newchnl.entity.resource.ChnlResBusiGoods;
import com.ailk.newchnl.entity.resource.ResGoods;
import com.ailk.newchnl.entity.xt.BusiThirdXXAssess;
import com.ailk.newchnl.mybatis.pagination.PageData;
import com.ailk.newchnl.mybatis.pagination.PageParameter;
import com.ailk.newchnl.util.*;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.*;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

/**
 * Created by admin on 2015/5/5.
 */
@Service("agentServiceFeeService")
@SuppressWarnings("all")
public class AgentServiceFeeServiceImpl implements AgentServiceFeeService {

    private static final Logger logger = LoggerFactory.getLogger(AgentServiceFeeServiceImpl.class);

    @Resource
    private ChannelEntityRelInfoDao channelEntityRelInfoDao;
    @Resource
    private AgentServiceFeeDao agentServiceFeeDao;
    @Resource
    private AgentFeeDtlDao agentFeeDtlDao;
    @Resource
    private ChannelEntityAccRelDao channelEntityAccRelDao;
    @Resource
    private ChnlResBusiGoodsDao chnlResBusiGoodsDao;
    @Resource
    private ResGoodsDao resGoodsDao;
    @Resource
    private ChannelBusiRecordDao channelBusiRecordDao;

    @Resource
    private AgentServiceFeeAppraiseDao agentServiceFeeAppraiseDao;

    @Resource
    private ThreeSupportFeeAddDao threeSupportFeeAddDao;

    @Resource
    private OAOExamineAddDao oAOExamineAddDao;

    @Resource
    private AgentAssessFeeAppraiseDao agentAssessFeeAppraiseDao;

    @Resource
    private AgentAssessFeeJobContractDao agentAssessFeeJobContractDao;

    @Resource
    private LeagueNodeClassifyInfoDao leagueNodeClassifyInfoDao;

    @Resource
    private ChannelFuseItemDao channelFuseItemDao;

    @Resource
    private ChannelFuseedRigidTargetDao channelFuseedRigidTargetDao;

    @Resource
    private AgentServiceFeeService agentServiceFeeService;

    @Resource
    private  AgentMarketingSupportFeeDao agentMarketingSupportFeeDao;

    @Resource
    private ChannelEntityBasicInfoDao channelEntityBasicInfoDao;

    @Resource
    private CompanyAssessmentScoreDao companyAssessmentScoreDao;

    @Resource
    private TietongItemScoreDao tietongItemScoreDao;

    @Resource
    private CheckContentScoreDao checkContentScoreDao;

    @Resource
    private BusinessThirdSupportFeeDao businessThirdSupportFeeDao;

    @Resource
    private BusinessThirdProjectRatioDao businessThirdProjectRatioDao;

    @Resource
    private BusinessThirdProjectReachDao businessThirdProjectReachDao;

    @Resource
    private BusinessThirdAssessmentDao businessThirdAssessmentDao;

    @Resource
    private BusinessThirdPersonMarketDao businessThirdPersonMarketDao;

    @Resource
    private BusiThirdXXAssessDao busiThirdXXAssessDao;

    @Resource
    private ThreeNodeNameFeeDao threeNodeNameFeeDao;

    @Resource
    private AgentAdjustUseDao agentAdjustUseDao;

    @Resource
    private CompanySpecSettlementDao companySpecSettlementDao;

    @Resource
    private BusiFuseProDao busiFuseProDao;

    @Resource
    private BusiThirdSalesRoleUpdDao busiThirdSalesRoleUpdDao;

    @Resource
    private ChannelSysBaseTypeDao channelSysBaseTypeDao;

    @Resource
    private ChannelNodeDao channelNodeDao;

    @Resource
    private ChannelNodeGainAssessManageDao channelNodeGainAssessManageDao;

    @Resource
    private BusiThirdSetDateDao busiThirdSetDateDao;

    //发送短信服务
    @Resource
    private ChannelNotifyService channelNotifyService;

    @Resource
    private DependencyBusiThirdXXJSJLDao dependencyBusiThirdXXJSJLDao;

    @Resource
    private RwdTpmsRegionAwardDao rwdTpmsRegionAwardDao;

    @Resource
    private BusiThirdPriceServiceRatioDao busiThirdPriceServiceRatioDao;
    
    @Resource
    private BusiThirdSpecialOfferInfoDao busiThirdSpecialOfferInfoDao;

    @Resource
    private TietongOnlineEvalDao tietongOnlineEvalDao;

    @Resource
    private TieTongSupportFeeAddDao tieTongSupportFeeAddDao;

    @Resource
    private BusiThirdDependRoleDao busiThirdDependRoleDao;

    @Resource
    private MidHighEndRetentionAssessmentDao midHighEndRetentionAssessmentDao;


    /**
     * 服务费录入
     *
     * @param channelEntityId
     * @param billMonth
     * @param countFee
     * @param totalFee
     * @param totalPoint
     * @param deductFee
     * @param notes
     * @return
     */
    @Override
    public Map<String, String> agentServiceFeeSave(Long channelEntityId, Long billMonth, Long countFee,
                                                   Long totalFee, Long totalPoint, Long deductFee, String notes, SPrivData sPrivData) throws Exception {

        Map<String, String> map = new HashMap<String, String>();
        try {
            //查询网点所属的代理商
            ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
            channelEntityRelInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            channelEntityRelInfo.setChannelEntityId(channelEntityId);
            List<ChannelEntityRelInfo> channelEntityRelInfoList = channelEntityRelInfoDao.query(channelEntityRelInfo);
            Long agentId = channelEntityRelInfoList.get(0).getParentEntity();

            //录入服务费
            AgentServiceFee agentServiceFee = new AgentServiceFee();
            agentServiceFee.setNodeId(channelEntityId);
            agentServiceFee.setAgentId(agentId);
            agentServiceFee.setBillMonth(billMonth);
            agentServiceFee.setCountfee(countFee == null ? 0 : countFee);
            agentServiceFee.setTotalFee(totalFee == null ? 0 : totalFee);
            agentServiceFee.setTotalPoint(totalPoint == null ? 0 : totalPoint);
            agentServiceFee.setDeductFee(deductFee == null ? 0 : deductFee);
            agentServiceFee.setNotes(notes);
            agentServiceFee.setNodeType(14L);
            agentServiceFee.setDoneDate(DateUtil.getCurrDate());
            agentServiceFee.setOperId(sPrivData.getOpId());
            agentServiceFee.setOrgId(sPrivData.getOrgId());
            int count = agentServiceFeeDao.insert(agentServiceFee);
            if (1 == count) {
                map.put("status", "0");
                map.put("msg", "保存成功!");
            }
        } catch (Exception e) {
            logger.error("合作厅服务费录入失败：", e);
            map.put("status", "1");
            map.put("msg", "保存失败!数据库里已有记录!");
        }
        return map;
    }

    /**
     * 服务费查询
     *
     * @param nodeId
     * @param billMonth
     * @param page
     * @return
     * @throws Exception
     */
    public PageData<AgentServiceFeeDtl> serviceFeeQuery(Long nodeId, Long billMonth, PageParameter page) throws Exception {
        try {
            List<AgentServiceFeeDtl> agentServiceFeeDtlLis1 = new ArrayList<AgentServiceFeeDtl>();
            List<AgentServiceFeeDtl> agentServiceFeeDtlList = agentServiceFeeDao.getAgentServiceFeeDtlList(nodeId, billMonth);
            if (0 == agentServiceFeeDtlList.size()) {
                return null;
            }
            String totalPointStr1 = "";
            String countFeeStr1 = "";
            String deductFeeStr1 = "";
            String totalFeeStr1 = "";
            AgentServiceFeeDtl agentServiceFeeDtl = null;
            for (int i = 0; i < agentServiceFeeDtlList.size(); i++) {
                agentServiceFeeDtl = agentServiceFeeDtlList.get(i);
                String totalPointStr = String.valueOf(agentServiceFeeDtl.getTotalPoint());
                String countFeeStr = String.valueOf(agentServiceFeeDtl.getCountfee());
                String deductFeeStr = String.valueOf(agentServiceFeeDtl.getDeductFee());
                String totalFeeStr = String.valueOf(agentServiceFeeDtl.getTotalFee());
                try {
                    if (totalPointStr.equals("0") || totalPointStr.equals("0.0")) {
                        agentServiceFeeDtl.setTotalPintStr("0.00");
                    } else {
                        double a = new Double(totalPointStr);
                        a = a / 100;
                        DecimalFormat df = new DecimalFormat();
                        df.setMaximumFractionDigits(2);
                        df.setMinimumFractionDigits(2);
                        df.setGroupingUsed(false);
                        totalPointStr = df.format(a);  //将数据格式化保存double类型
                        totalPointStr1 = totalPointStr;
                        agentServiceFeeDtl.setTotalPintStr(totalPointStr);
                    }
                } catch (Exception e) {
                    logger.error("分转换元出错", e);
                }
                try {
                    if (countFeeStr.equals("0")) {
                        agentServiceFeeDtl.setCountfeeStr("0.00");
                    } else {
                        double a = new Double(countFeeStr);
                        a = a / 100;
                        DecimalFormat df = new DecimalFormat();
                        df.setMaximumFractionDigits(2);
                        df.setMinimumFractionDigits(2);
                        df.setGroupingUsed(false);
                        countFeeStr = df.format(a);  //将数据格式化保存double类型
                        countFeeStr1 = countFeeStr;
                        agentServiceFeeDtl.setCountfeeStr(countFeeStr);
                    }
                } catch (Exception e) {
                    logger.error("分转换元出错", e);
                }
                try {
                    if (deductFeeStr.equals("0")) {
                        agentServiceFeeDtl.setDeductFeeStr("0.00");
                    } else {
                        double a = new Double(deductFeeStr);
                        a = a / 100;
                        DecimalFormat df = new DecimalFormat();
                        df.setMaximumFractionDigits(2);
                        df.setMinimumFractionDigits(2);
                        df.setGroupingUsed(false);
                        deductFeeStr = df.format(a);  //将数据格式化保存double类型
                        deductFeeStr1 = deductFeeStr;
                        agentServiceFeeDtl.setDeductFeeStr(deductFeeStr);
                    }
                } catch (Exception e) {
                    logger.error("分转换元出错", e);
                }
                try {
                    if (totalFeeStr.equals("0")) {
                        agentServiceFeeDtl.setTotalFeeStr("0.00");
                    } else {
                        double a = new Double(totalFeeStr);
                        a = a / 100;
                        DecimalFormat df = new DecimalFormat();
                        df.setMaximumFractionDigits(2);
                        df.setMinimumFractionDigits(2);
                        df.setGroupingUsed(false);
                        totalFeeStr = df.format(a);  //将数据格式化保存double类型
                        totalFeeStr1 = totalFeeStr;
                        agentServiceFeeDtl.setTotalFeeStr(totalFeeStr);
                    }
                } catch (Exception e) {
                    logger.error("分转换元出错", e);
                }
                agentServiceFeeDtlLis1.add(agentServiceFeeDtl);
            }

            agentServiceFeeDtlList.add(agentServiceFeeDtl);
            agentServiceFeeDtl = new AgentServiceFeeDtl();
            agentServiceFeeDtl.setAgentName("总计");
            agentServiceFeeDtl.setBillMonth(billMonth);
            agentServiceFeeDtl.setTotalPintStr(totalPointStr1);
            agentServiceFeeDtl.setCountfeeStr(countFeeStr1);
            agentServiceFeeDtl.setDeductFeeStr(deductFeeStr1);
            agentServiceFeeDtl.setTotalFeeStr(totalFeeStr1);
            agentServiceFeeDtlLis1.add(agentServiceFeeDtl);

            return new PageData<AgentServiceFeeDtl>(agentServiceFeeDtlLis1, page);

        } catch (Exception e) {
            logger.error("服务费查询失败：", e);
            return null;
        }
    }

    /**
     * 服务费修改
     *
     * @param channelEntityId
     * @param billMonth
     * @param countFee
     * @param totalFee
     * @param totalPoint
     * @param deductFee
     * @param notes
     * @param sPrivData
     * @return
     * @throws Exception
     */
    public Map<String, String> agentServiceFeeUpdate(Long channelEntityId, Long billMonth, Long countFee, Long totalFee,
                                                     Long totalPoint, Long deductFee, String notes, SPrivData sPrivData) throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        AgentServiceFee agentServiceFee = new AgentServiceFee();
        agentServiceFee.setNodeId(channelEntityId);
        agentServiceFee.setBillMonth(billMonth);
        agentServiceFee.setCountfee(countFee);
        agentServiceFee.setTotalFee(totalFee);
        agentServiceFee.setTotalPoint(totalPoint);
        agentServiceFee.setDeductFee(deductFee);
        agentServiceFee.setNotes(notes);
        agentServiceFee.setOperId(sPrivData.getOpId());
        agentServiceFee.setOrgId(sPrivData.getOrgId());
        agentServiceFee.setDoneDate(DateUtil.getCurrDate());
        agentServiceFee.setNodeType(14L);
        agentServiceFeeDao.update(agentServiceFee);
        map.put("status", "0");
        map.put("msg", "合作方服务费修改成功");
        return map;
    }

    /**
     * 服务费删除
     *
     * @param nodeId
     * @param billMonth
     * @return
     * @throws Exception
     */
    public Map<String, String> serviceFeeDelete(Long nodeId, Long billMonth) throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        AgentServiceFee agentServiceFee = new AgentServiceFee();
        agentServiceFee.setNodeId(nodeId);
        agentServiceFee.setBillMonth(billMonth);
        agentServiceFee.setNodeType(14L);
        agentServiceFeeDao.delete(agentServiceFee);
        map.put("status", "0");
        map.put("msg", "删除成功！");
        return map;
    }

    /**
     * 欠费录入前验证
     *
     * @param channelEntityId
     * @param accId
     * @param billMonth
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, String> findAgentFee(Long channelEntityId, Long accId, Integer billMonth) throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        AgentFeeDtl agentFeeDtl = new AgentFeeDtl();
        agentFeeDtl.setAgentId(channelEntityId);
        agentFeeDtl.setAccId(accId);
        agentFeeDtl.setBillMonth(billMonth);
        agentFeeDtl.setAccCode(ChannelConstants.AGENT_DEFAULT_FEE);
        try {
            List<AgentFeeDtl> agentFeeDtlList = agentFeeDtlDao.query(agentFeeDtl);
            if (0 == agentFeeDtlList.size()) {
                map.put("status", "0");
                map.put("msg", "");
            } else {
                map.put("status", "1");
                double a = agentFeeDtlList.get(0).getTotalFee() / 100.0;
                map.put("msg", "已经录入" + a + "酬金(元)，是否确认覆盖？");
            }
        } catch (Exception e) {
            logger.error("欠费录入前验证失败：", e);
            map.put("status", "1");
            map.put("msg", "保存失败!数据库里已有记录!");
        }
        return map;
    }

    /**
     * 欠费录入
     *
     * @param channelEntityId
     * @param billMonth
     * @param accId
     * @param totalFee
     * @param notes
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, String> feeDefaultFeeAdd(Long channelEntityId, Integer billMonth, Long accId, Long totalFee, String notes) throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        AgentFeeDtl agentFeeDtl = new AgentFeeDtl();
        agentFeeDtl.setAgentId(channelEntityId);
        agentFeeDtl.setAccId(accId);
        agentFeeDtl.setBillMonth(billMonth);
        agentFeeDtl.setAccCode(ChannelConstants.AGENT_DEFAULT_FEE);
        ChannelEntityAccRel channelEntityAccRel = new ChannelEntityAccRel();
        channelEntityAccRel.setChannelEntityId(channelEntityId);
        try {
            List<ChannelEntityAccRel> channelEntityAccRelList = channelEntityAccRelDao.query(channelEntityAccRel);
            agentFeeDtl.setAccId(channelEntityAccRelList.get(0).getAccId());

            List<AgentFeeDtl> agentFeeDtlList = agentFeeDtlDao.query(agentFeeDtl);
            if (0 == agentFeeDtlList.size()) {
                agentFeeDtl.setTotalFee(totalFee);
                agentFeeDtl.setResCode(0L);
                agentFeeDtl.setAmount(0);
                agentFeeDtl.setRate(0);
                agentFeeDtlDao.insert(agentFeeDtl);
            } else {
                agentFeeDtl.setTotalFee(totalFee);
                agentFeeDtlDao.update(agentFeeDtl);
            }
            map.put("status", "0");
            map.put("msg", "代理商欠费录入成功");
        } catch (Exception e) {
            logger.error("代理商欠费录入失败：", e);
            map.put("status", "1");
            map.put("msg", "代理商欠费录入失败");
        }
        return map;
    }

    /**
     * 欠费查询
     *
     * @param billMonth
     * @param accId
     * @param page
     * @return
     * @throws Exception
     */
    public PageData<AdjustFeeDtl> feeDefaultFeeQuery(Integer billMonth, Long accId, PageParameter page) throws Exception {
        List<AdjustFeeDtl> adjustFeeDtlList = agentFeeDtlDao.getAdjustFeeDtl2(accId, ChannelConstants.AGENT_DEFAULT_FEE, billMonth);
        List<AdjustFeeDtl> adjustFeeDtls = new ArrayList<AdjustFeeDtl>();
        for (int i = 0; i < adjustFeeDtlList.size(); i++) {
            AdjustFeeDtl feeDtl = adjustFeeDtlList.get(i);
            String amountFee = String.valueOf(feeDtl.getTotalFeeMonty());
            try {
                if (amountFee.equals("0")) {
                    feeDtl.setTotalFeeStr("0.00");
                } else {
                    double a = new Double(amountFee);
                    a = a / 100;
                    DecimalFormat df = new DecimalFormat();
                    df.setMaximumFractionDigits(2);
                    df.setMinimumFractionDigits(2);
                    df.setGroupingUsed(false);
                    amountFee = df.format(a);  //将数据格式化保存double类型
                    feeDtl.setTotalFeeStr(amountFee);
                }
            } catch (Exception e) {
                logger.error("将业务酬金元转换分出错", e);
            }
            adjustFeeDtls.add(feeDtl);
        }
        AdjustFeeDtl adjustFeeDtl = new AdjustFeeDtl();
        adjustFeeDtl.setChannelEntityName("总计");
        String amountFee = String.valueOf(adjustFeeDtlList.get(0).getTotalFeeMonty());
        try {
            if (amountFee.equals("0")) {
                adjustFeeDtl.setTotalFeeStr("0.00");
            } else {
                double a = new Double(amountFee);
                a = a / 100;
                DecimalFormat df = new DecimalFormat();
                df.setMaximumFractionDigits(2);
                df.setMinimumFractionDigits(2);
                df.setGroupingUsed(false);
                amountFee = df.format(a);  //将数据格式化保存double类型
                adjustFeeDtl.setTotalFeeStr(amountFee);
            }
        } catch (Exception e) {
            logger.error("将业务酬金元转换分出错", e);
        }

        adjustFeeDtls.add(adjustFeeDtl);
        return new PageData<AdjustFeeDtl>(adjustFeeDtls, page);
    }

    /**
     * 业务用品查询
     *
     * @param resType
     * @param resCode
     * @param orgId
     * @param page
     * @return
     * @throws Exception
     */
    public PageData<ChnlResBusiGoods> busiGoodsQuery(Long resType, Long resCode, Long orgId, PageParameter page) throws Exception {
        ChnlResBusiGoods chnlResBusiGoods = new ChnlResBusiGoods();
        chnlResBusiGoods.setOrgId(orgId);

        List<ChnlResBusiGoods> chnlResBusiGoodsList = new ArrayList<ChnlResBusiGoods>();
        //资源类别为全部
        if (0 == resType) {
            if (0 == resCode) {
                Long[] resCodes = new Long[4];
                resCodes[0] = 702001L;
                resCodes[1] = 702002L;
                resCodes[2] = 702003L;
                resCodes[3] = 701001L;
                chnlResBusiGoods.setResCodes(resCodes);
                chnlResBusiGoodsList = chnlResBusiGoodsDao.queryByChnlResBusiGoods(chnlResBusiGoods);
            } else {
                chnlResBusiGoods.setResCode(resCode);
                chnlResBusiGoodsList = chnlResBusiGoodsDao.queryByChnlResBusiGoods(chnlResBusiGoods);
            }
        } else if (1 == resType) {//资源类别为数据业务折扣卡（用品）
            if (0 == resCode) {
                Long[] resCodes = new Long[3];
                resCodes[0] = 702001L;
                resCodes[1] = 702002L;
                resCodes[2] = 702003L;
                chnlResBusiGoods.setResCodes(resCodes);
                chnlResBusiGoodsList = chnlResBusiGoodsDao.queryByChnlResBusiGoods(chnlResBusiGoods);
            } else {
                chnlResBusiGoods.setResCode(resCode);
                chnlResBusiGoodsList = chnlResBusiGoodsDao.queryByChnlResBusiGoods(chnlResBusiGoods);
            }
        } else if (2 == resType) {
            chnlResBusiGoods.setResCode(resCode);
            chnlResBusiGoodsList = chnlResBusiGoodsDao.queryByChnlResBusiGoods(chnlResBusiGoods);
        }

        return new PageData<ChnlResBusiGoods>(chnlResBusiGoodsList, page);
    }

    /**
     * 业务用品自营厅迁移
     *
     * @param doneCode
     * @param orgId
     * @return
     * @throws Exception
     */
    public Map<String, String> busiGoodsTransfer(Long doneCode, Long orgId) throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        try {
            ChnlResBusiGoods chnlResBusiGoods = new ChnlResBusiGoods();
            chnlResBusiGoods.setDoneCode(doneCode);
            chnlResBusiGoods.setOrgId(orgId);
            chnlResBusiGoodsDao.update(chnlResBusiGoods);
            map.put("status", "0");
            map.put("msg", "自营厅迁移成功");
        } catch (Exception e) {
            map.put("status", "1");
            map.put("msg", "自营厅迁移失败");
        }

        return map;
    }

    /**
     * 物品入库
     *
     * @param resType
     * @param resCode
     * @param goodsName
     * @param startIccId
     * @param amount
     * @param orgId
     * @param opId
     * @return
     * @throws Exception
     */
    public Map<String, String> resGoodsAdd(Long resType, Long resCode, String goodsName, Long startIccId, Long amount, Long orgId, Long opId) throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        try {
            ResGoods resGoods = new ResGoods();
            resGoods.setResCode(resCode);
            resGoods.setMemo(goodsName);
//            resGoods.setStartIccId(startIccId);
            if (null != startIccId && 0 != startIccId) {
                resGoods.setStartIccId(startIccId + "");
                resGoods.setEndIccId((startIccId + amount - 1) + "");
            } else {
                resGoods.setStartIccId(" ");
            }
            resGoods.setAmount(amount);
            resGoods.setDoneDate(DateUtil.getCurrDate());
            resGoods.setReceiver(0);
            resGoods.setManageStatus(2);
            resGoods.setUserType(2);
            resGoods.setOpId(opId);
            resGoods.setOrgId(orgId);
            resGoods.setResId(channelBusiRecordDao.getSequence());
            resGoods.setDoneCode(channelBusiRecordDao.getSequence());
            resGoodsDao.insert(resGoods);

            map.put("status", "0");
            map.put("msg", "物品入库成功");
        } catch (Exception e) {
            logger.error("物品入库失败", e);
            map.put("status", "1");
            map.put("msg", "物品入库失败");
        }
        return map;
    }

    /**
     * 物品查询
     *
     * @param resType
     * @param resCode
     * @param orgId
     * @param page
     * @return
     * @throws Exception
     */
    public PageData<ResGoods> resGoodsQuery(Long resType, Long resCode, Long orgId, PageParameter page) throws Exception {
        ResGoods resGoods = new ResGoods();
        resGoods.setOrgId(orgId);
        if (resCode != 903000) {
            resGoods.setResCode(resCode);
        }
        List<ResGoods> resGoodsList = resGoodsDao.query(resGoods);
        return new PageData<ResGoods>(resGoodsList, page);
    }

    /**
     * 物品自营厅迁移
     *
     * @param resId
     * @param toOrgId
     * @return
     * @throws Exception
     */
    public Map<String, String> resGoodsTransfer(Long resId, Long toOrgId) throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        try {
            ResGoods resGoods = new ResGoods();
            resGoods.setOrgId(toOrgId);
            resGoods.setResId(resId);
            resGoodsDao.update(resGoods);
            map.put("status", "0");
            map.put("msg", "物品自营厅迁移成功");
        } catch (Exception e) {
            logger.error("自营厅迁移失败", e);
            map.put("status", "1");
            map.put("msg", "物品自营厅迁移失败");
        }
        return map;
    }

    /**
     * 物品修改
     *
     * @param resCode
     * @param goodsName
     * @param startIccId
     * @param amount
     * @param opId
     * @return
     * @throws Exception
     */
    public Map<String, String> resGoodsModify(Long resId, Long resCode, String goodsName, Long startIccId, Long amount, Long opId) throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        try {
            ResGoods resGoods = new ResGoods();
            resGoods.setResId(resId);
            resGoods.setResCode(resCode);
            resGoods.setMemo(goodsName);
            if (null != startIccId && 0 != startIccId) {
                resGoods.setStartIccId(startIccId + "");
                resGoods.setEndIccId((startIccId + amount - 1) + "");
            } else {
                resGoods.setStartIccId(" ");
            }
            resGoods.setAmount(amount);
            resGoods.setOpId(opId);
            resGoodsDao.update(resGoods);

            map.put("status", "0");
            map.put("msg", "物品修改成功");
        } catch (Exception e) {
            logger.error("物品修改失败", e);
            map.put("status", "1");
            map.put("msg", "物品修改失败");
        }
        return map;
    }

    //第三方行销项目支撑费录入
    @Override
    public void addThreeSupportFee(ThreeSupportFeeAdd threeSupportFeeAdd ,SPrivData sPrivData) throws Exception{
        List<ThreeSupportFeeAdd> addThreeSupportFeeList = threeSupportFeeAddDao.query(threeSupportFeeAdd);
        if(addThreeSupportFeeList.size()>0){
            throw new Exception("该条记录已录入，请勿重复添加");
        }
        //获取序列号
        Long doneCode = threeSupportFeeAddDao.getSequence();
        try {
            threeSupportFeeAdd.setRecStatus(0);
            threeSupportFeeAdd.setDoneDate(new Date());
            threeSupportFeeAdd.setDoneCode(doneCode);
            threeSupportFeeAddDao.insert(threeSupportFeeAdd);
        }catch (Exception e){
            logger.error("插入数据库异常");
        }
        try{
            //查询审批人信息
            AgentAdjustUse adjustUse = new AgentAdjustUse();
            adjustUse.setAgentAdjustType(2);
            adjustUse.setAgentAdjustUseId(threeSupportFeeAdd.getAgentAdjustUseId()); // 将id临时保存的，暂存
            List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(adjustUse);
            //开始定义短信信息，并调接口发送至审批人手机
            String name =sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-第三方行销项目支撑费考核审批-" +
                    DateUtil.formatDate(new Date(),"yyyyMMdd") + "-" + doneCode;
            String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为"+name+"";
            logger.info(messager1);
            String smsCode = "100011051243";
            SPrivData sPrivData1 = sPrivData;
            sPrivData1.setOpId(999990131L);
            sPrivData1.setOrgId(0L);
            channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);

        }catch (Exception e){
            logger.error("发送短信给审批人异常，失败原因是：" + e);
        }

    }

    //第三方行销项目支撑费管理
    @Override
    public PageData<ThreeSupportFeeAdd> ManageThreeSupportFee(String begUpLoadDate,String endUpLoadDate, PageParameter page) {
        List<ThreeSupportFeeAdd> threeSupportFeeAddList = threeSupportFeeAddDao.queryThreeSupportFeeAdd(begUpLoadDate,endUpLoadDate, page);
        if (threeSupportFeeAddList.size()>0){
            for (ThreeSupportFeeAdd entity:threeSupportFeeAddList) {
                if (entity.getRecStatus() == 0){
                    entity.setRecStatusApprove("待审批");
                }else if (entity.getRecStatus() == 1){
                    entity.setRecStatusApprove("审批通过");
                }else if (entity.getRecStatus() == 2){
                    entity.setRecStatusApprove("审批驳回");
                }
            }
        }
        return new PageData<ThreeSupportFeeAdd>(threeSupportFeeAddList, page);
    }

    //第三方行销项目支撑费审批查询
    @Override
    public PageData<ThreeSupportFeeAdd> threeSupportFeeInfoQuery(ThreeSupportFeeAdd threeSupportFeeAdd, PageParameter pageParameter) {
        List<ThreeSupportFeeAdd> threeSupportFeeList =null;
        try {
            String userName = threeSupportFeeAdd.getUserName();
            Long agentAdjustUseId = agentAdjustUseDao.getIdByUserName(userName);
            if (agentAdjustUseId != null){
                threeSupportFeeAdd.setAgentAdjustUseId(agentAdjustUseId);
            }else{
                logger.info("没有该审批人审批信息！！！");
                return new PageData<ThreeSupportFeeAdd>(threeSupportFeeList, pageParameter);
            }
            threeSupportFeeList = threeSupportFeeAddDao.queryThreeSupportFeeAddInfo(threeSupportFeeAdd, pageParameter);
            if (threeSupportFeeList.size()>0){
                for (ThreeSupportFeeAdd entity:threeSupportFeeList) {
                    if (entity.getRecStatus() == 0){
                        entity.setRecStatusApprove("待审批");
                    }else if (entity.getRecStatus() == 1){
                        entity.setRecStatusApprove("审批通过");
                    }else if (entity.getRecStatus() == 2){
                        entity.setRecStatusApprove("审批驳回");
                    }
                    entity.setDoneDate(null);
                }
            }
            return new PageData<ThreeSupportFeeAdd>(threeSupportFeeList, pageParameter.getTotalCount());
        }catch (Exception e){
            logger.error("");
        }
        return new PageData<ThreeSupportFeeAdd>(threeSupportFeeList, pageParameter.getTotalCount());
    }

    @Override
    public void threeSupportFeeAddEdit(ThreeSupportFeeAdd threeSupportFeeAdd) throws Exception {
        //因审批和修改走同一套逻辑 故判断其控制层如果没有传 状态 则状态给-1：失效  传了状态直接给 2：审批通过 或 3：审批驳回
        //对传了状态的数据 直接进行修改，而数据修改的 需要把原先数据置失效，再添加数据
        Date date = new Date();
        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMM");
        Calendar cal=Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH,-1);
        String billMonth=sdf.format(cal.getTime());
        ThreeSupportFeeAdd threeSupportFeetemp = threeSupportFeeAdd;
        if (threeSupportFeetemp.getRecStatus() == null) {
            threeSupportFeetemp.setRecStatus(-1);
        }
        try{
            if(threeSupportFeetemp.getRecStatus() == -1){
                threeSupportFeeAddDao.update(threeSupportFeetemp);
                threeSupportFeeAdd.setDoneDate(new Date());
                threeSupportFeeAdd.setRecStatus(0);
                threeSupportFeeAddDao.insert(threeSupportFeeAdd);
            } else {
                threeSupportFeetemp.setDoneDate(new Date());
                threeSupportFeetemp.setSupportTime(billMonth);
                threeSupportFeeAddDao.update(threeSupportFeetemp);
            }
        }catch (Exception e){
            logger.error("修改数据失败，失败原因是："+e);
            throw new Exception("修改数据失败，失败原因" +e);
        }
    }


    //物流OAO服务项目供应商服务费考核录入
    @Override
    public void addOAOExamine(OAOExamineAdd oaoExamineAdd) throws Exception{
        String logMen = oaoExamineAdd.getLogisticsMen();
        if(logMen.equals("0")){
            oaoExamineAdd.setLogisticsMen("上海顺衡物流有限公司（顺衡物流受理权限）");
        }else if(logMen.equals("1")){
            oaoExamineAdd.setLogisticsMen("北京京邦达贸易有限公司（圆迈贸易受理权限）");
        }
        oaoExamineAdd.setRecStatus(1);
        Date date = new Date();
        oaoExamineAdd.setGoTableTime(date);
        SimpleDateFormat ft = new SimpleDateFormat("yyyyMM");
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-1);
        date = calendar.getTime();
        String month = ft.format(date);
        oaoExamineAdd.setTheMonth(month);

        //判断库里是否存在该条数据
        List<OAOExamineAdd> addOAOExamineAddList = oAOExamineAddDao.query(oaoExamineAdd);
        if(addOAOExamineAddList.size()>0){
            throw new Exception("该月供应商记录已录入，请勿重复添加");
        }
        try {
            oAOExamineAddDao.insert(oaoExamineAdd);
        }catch (Exception e){
            logger.error("插入数据库异常");
        }

    }
    //物流OAO服务项目供应商服务费考核管理
    @Override
    public PageData<OAOExamineAdd> ManageOAOExamine(OAOExamineAdd oaoExamineAdd, PageParameter page) {
        String logMen = oaoExamineAdd.getLogisticsMen();
        if(logMen.equals("0")){
            oaoExamineAdd.setLogisticsMen("上海顺衡物流有限公司（顺衡物流受理权限）");
        }else if(logMen.equals("1")){
            oaoExamineAdd.setLogisticsMen("北京京邦达贸易有限公司（圆迈贸易受理权限）");
        }
        List<OAOExamineAdd> OAOExamineAddList = null;
        try {
            OAOExamineAddList = oAOExamineAddDao.queryOAOExamineAdd(oaoExamineAdd, page);
            for (OAOExamineAdd entity: OAOExamineAddList) {
                if (entity.getMyRemark().contains(">") || entity.getMyRemark().contains("<")){
                    if (entity.getMyRemark().contains(">")){
                        entity.setMyRemark(entity.getMyRemark().replace(">","&gt;"));
                    }
                    if (entity.getMyRemark().contains("<")){
                        entity.setMyRemark(entity.getMyRemark().replace("<","&lt;"));
                    }
                }
            }
        }catch (Exception e){
            logger.error("" + e);
        }

        return new PageData<OAOExamineAdd>(OAOExamineAddList, page);
    }

    @Override
    public void EditOAOExamine(OAOExamineAdd oAOExamineAdd) throws Exception {

        OAOExamineAdd oAOExamineAddTemp = oAOExamineAdd;
        oAOExamineAddTemp.setRecStatus(0);

        try{
            oAOExamineAddDao.update(oAOExamineAddTemp);
            oAOExamineAdd.setRecStatus(1);
            oAOExamineAddDao.insert(oAOExamineAdd);
        }catch (Exception e){
            logger.error(""+e);
        }
    }


    @Override
    public void addAgentServiceFeeAppraise(AgentServiceFeeAppraise agentServiceFeeAppraise) throws Exception {
        //判断记录是否存在
        List<AgentServiceFeeAppraise> agentServiceFeeAppraiseList = agentServiceFeeAppraiseDao.query(agentServiceFeeAppraise);
        if (agentServiceFeeAppraiseList.size() > 0) {
            throw new Exception("该营业厅本月已录入，请至加盟营业厅服务费考核管理页面进行修改。");
        }
        agentServiceFeeAppraise.setRecStatus(1);
        agentServiceFeeAppraise.setDoneDate(new Date());
        agentServiceFeeAppraiseDao.insert(agentServiceFeeAppraise);
    }

    @Override
    public void addAgentMarketingSupportFee(AgentMarketingSupportFee agentMarketingSupportFee) throws Exception {
        //判断记录是否存在
        List<AgentMarketingSupportFee> agentMarketingSupportFeeList = agentMarketingSupportFeeDao.query(agentMarketingSupportFee);
        if (agentMarketingSupportFeeList.size() > 0) {
            throw new Exception("该营业厅本月已录入，请至第三方行销项目营销支撑费考核管理页面进行修改。");
        }
        agentMarketingSupportFee.setRecStatus(1);
        agentMarketingSupportFee.setDoneDate(new Date());
        agentMarketingSupportFeeDao.insert(agentMarketingSupportFee);

    }

    @Override
    public void delAgentMarketingSupportFee(AgentMarketingSupportFee agentMarketingSupportFee)throws Exception {
        agentMarketingSupportFee.setRecStatus(0);
        agentMarketingSupportFeeDao.update(agentMarketingSupportFee);

    }

    @Override
    public PageData<BusinessThirdSupportFee> queryBusinessThirdSupportFee(BusinessThirdSupportFee businessThirdSupportFee, PageParameter page) {
        List<BusinessThirdSupportFee> businessThirdSupportFeeList = businessThirdSupportFeeDao.queryBusinessThirdSupportFee(businessThirdSupportFee,page);
        return new PageData<BusinessThirdSupportFee>(businessThirdSupportFeeList,page);
    }

    @Override
    public void addBusinessThirdSupportFee(BusinessThirdSupportFee businessThirdSupportFee) throws Exception{
        //判断记录是否存在
        List<BusinessThirdSupportFee> businessThirdSupportFeeList = businessThirdSupportFeeDao.query(businessThirdSupportFee);
        if(businessThirdSupportFeeList.size() > 0){
            throw new Exception("该营业厅本月已录入，请至体验营业厅第三方人员支撑项目营销支撑费考核管理页面进行修改。");
        }
        businessThirdSupportFee.setRecStatus(1);
        businessThirdSupportFee.setDoneDate(new Date());
        businessThirdSupportFeeDao.insert(businessThirdSupportFee);

    }

    @Override
    public void delBusinessThirdSupportFee(BusinessThirdSupportFee businessThirdSupportFee) throws Exception{
        businessThirdSupportFee.setRecStatus(0);
        businessThirdSupportFeeDao.update(businessThirdSupportFee);

    }

    @Override
    public void editBusinessThirdSupportFee(BusinessThirdSupportFee businessThirdSupportFee) throws Exception {
        BusinessThirdSupportFee businessThirdSupportFeeTemp = new BusinessThirdSupportFee();
        businessThirdSupportFeeTemp.setChannelEntityId(businessThirdSupportFee.getChannelEntityId());
        businessThirdSupportFeeTemp.setBillMonth(businessThirdSupportFee.getBillMonth());
        businessThirdSupportFeeTemp.setRecStatus(0);
        businessThirdSupportFeeDao.update(businessThirdSupportFeeTemp);
        businessThirdSupportFee.setRecStatus(1);
        businessThirdSupportFeeDao.insert(businessThirdSupportFee);

    }


    @Override
    public void addAgentAssessFeeAppraise(AgentAssessFeeAppraise agentAssessFeeAppraise) throws Exception {
        //判断记录是否存在
        List<AgentAssessFeeAppraise> agentAssessFeeAppraiseList = agentAssessFeeAppraiseDao.query(agentAssessFeeAppraise);
        if (agentAssessFeeAppraiseList.size() > 0) {
            throw new Exception("该营业厅本月已录入，请至加盟营业厅营销支撑费考核管理页面进行修改。");
        }
        agentAssessFeeAppraise.setRecStatus(1);
        agentAssessFeeAppraise.setDoneDate(new Date());
        agentAssessFeeAppraiseDao.insert(agentAssessFeeAppraise);
    }

    @Override
    public void addAgentAssessFeeJobContract(AgentAssessFeeJobContract agentAssessFeeJobContract) throws Exception {
        //判断记录是否存在
        List<AgentAssessFeeJobContract> agentAssessFeeJobContractList = agentAssessFeeJobContractDao.query(agentAssessFeeJobContract);
        if (agentAssessFeeJobContractList.size() > 0) {
            throw new Exception("该营业厅本月已录入，请至加盟营业厅营销支撑费考核管理页面进行修改。");
        }
        agentAssessFeeJobContract.setRecStatus(1);
        agentAssessFeeJobContract.setDoneDate(new Date());
        agentAssessFeeJobContractDao.insert(agentAssessFeeJobContract);
    }



    @Override
    public void delAgentServiceFeeAppraise(AgentServiceFeeAppraise agentServiceFeeAppraise) throws Exception {
        agentServiceFeeAppraise.setRecStatus(0);
        agentServiceFeeAppraiseDao.update(agentServiceFeeAppraise);
    }

    @Override
    public void delAgentAssessFeeJobContract(AgentAssessFeeJobContract agentAssessFeeJobContract) throws Exception {
        agentAssessFeeJobContract.setRecStatus(0);
        agentAssessFeeJobContractDao.update(agentAssessFeeJobContract);
    }

    @Override
    public void delAgentAssessFeeAppraise(AgentAssessFeeAppraise agentAssessFeeAppraise) throws Exception {
        agentAssessFeeAppraise.setRecStatus(0);
        agentAssessFeeAppraiseDao.update(agentAssessFeeAppraise);
    }

    @Override
    public void editAgentServiceFeeAppraise(AgentServiceFeeAppraise agentServiceFeeAppraise) throws Exception {
        AgentServiceFeeAppraise agentServiceFeeAppraiseTemp = new AgentServiceFeeAppraise();
        agentServiceFeeAppraiseTemp.setChannelEntityId(agentServiceFeeAppraise.getChannelEntityId());
        agentServiceFeeAppraiseTemp.setBillMonth(agentServiceFeeAppraise.getBillMonth());
        agentServiceFeeAppraiseTemp.setRecStatus(0);
        agentServiceFeeAppraiseDao.update(agentServiceFeeAppraiseTemp);


        agentServiceFeeAppraise.setRecStatus(1);
        agentServiceFeeAppraiseDao.insert(agentServiceFeeAppraise);
    }

    @Override
    public void editAgentAssessFeeAppraise(AgentAssessFeeAppraise agentAssessFeeAppraise) throws Exception {
        AgentAssessFeeAppraise agentAssessFeeAppraiseTemp = new AgentAssessFeeAppraise();
        agentAssessFeeAppraiseTemp.setChannelEntityId(agentAssessFeeAppraise.getChannelEntityId());
        agentAssessFeeAppraiseTemp.setBillMonth(agentAssessFeeAppraise.getBillMonth());
        agentAssessFeeAppraiseTemp.setRecStatus(0);
        agentAssessFeeAppraiseDao.update(agentAssessFeeAppraiseTemp);


        agentAssessFeeAppraise.setRecStatus(1);
        agentAssessFeeAppraiseDao.insert(agentAssessFeeAppraise);
    }

    @Override
    public void editAgentAssessFeeJobContract(AgentAssessFeeJobContract agentAssessFeeJobContract) throws Exception {
        AgentAssessFeeJobContract agentAssessFeeJobContractTemp = new AgentAssessFeeJobContract();
        agentAssessFeeJobContractTemp.setChannelEntityId(agentAssessFeeJobContract.getChannelEntityId());
        agentAssessFeeJobContractTemp.setBillMonth(agentAssessFeeJobContract.getBillMonth());
        agentAssessFeeJobContractTemp.setRecStatus(0);
        agentAssessFeeJobContractDao.update(agentAssessFeeJobContractTemp);

        agentAssessFeeJobContract.setRecStatus(1);
        agentAssessFeeJobContractDao.insert(agentAssessFeeJobContract);
    }

    @Override
    public PageData<AgentServiceFeeAppraise> queryAgentServiceFeeAppraise(AgentServiceFeeAppraise agentServiceFeeAppraise, PageParameter page) {
        List<AgentServiceFeeAppraise> agentServiceFeeAppraiseList = agentServiceFeeAppraiseDao.queryAgentServiceFeeAppraise(agentServiceFeeAppraise, page);
        return new PageData<AgentServiceFeeAppraise>(agentServiceFeeAppraiseList, page);
    }

    @Override
    public PageData<AgentMarketingSupportFee> queryMarketingSupportFee(AgentMarketingSupportFee agentMarketingSupportFee, PageParameter page) {
        List<AgentMarketingSupportFee> agentMarketingSupportFeeList = agentMarketingSupportFeeDao.queryMarketingSupportFee(agentMarketingSupportFee, page);
        return new PageData<AgentMarketingSupportFee>(agentMarketingSupportFeeList, page);
    }

    @Override
    public void editAgentMarketingSupportFee(AgentMarketingSupportFee agentMarketingSupportFee) throws Exception {
        AgentMarketingSupportFee agentMarketingSupportFeeTemp = new AgentMarketingSupportFee();
        agentMarketingSupportFeeTemp.setChannelEntityId(agentMarketingSupportFee.getChannelEntityId());
        agentMarketingSupportFeeTemp.setBillMonth(agentMarketingSupportFee.getBillMonth());
        agentMarketingSupportFeeTemp.setRecStatus(0);
        try {
            agentMarketingSupportFeeDao.update(agentMarketingSupportFeeTemp);
        }catch (Exception e){
            throw new Exception("修改失败，原因是：",e);
        }

        agentMarketingSupportFee.setRecStatus(1);
        try {
            agentMarketingSupportFeeDao.insert(agentMarketingSupportFee);
        }catch (Exception e){
            throw new Exception("======",e);
        }



    }

    @Override
    public PageData<AgentAssessFeeAppraise> queryAgentAssessFeeAppraise(AgentAssessFeeAppraise agentAssessFeeAppraise, PageParameter page) {
        List<AgentAssessFeeAppraise> agentAssessFeeAppraiseList = agentAssessFeeAppraiseDao.queryAgentAssessFeeAppraise(agentAssessFeeAppraise, page);
        return new PageData<AgentAssessFeeAppraise>(agentAssessFeeAppraiseList, page);
    }

    @Override
    public PageData<AgentAssessFeeJobContract> queryAgentAssessFeeJobContract(AgentAssessFeeJobContract agentAssessFeeJobContract, PageParameter page) {
        List<AgentAssessFeeJobContract> agentAssessFeeJobContractList = agentAssessFeeJobContractDao.queryAgentAssessFeeJobContract(agentAssessFeeJobContract, page);
        return new PageData<AgentAssessFeeJobContract>(agentAssessFeeJobContractList, page);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public void addLeagueNodeClassifyInfo(LeagueNodeClassifyInfo leagueNodeClassifyInfo) throws Exception {
        //判断记录是否存在
        List<LeagueNodeClassifyInfo> leagueNodeClassifyInfoList = leagueNodeClassifyInfoDao.query(leagueNodeClassifyInfo);
        if (leagueNodeClassifyInfoList.size() > 0) {
            throw new Exception("该营业厅已录入，请至加盟营业厅分类信息管理页面进行修改。");
        }
        leagueNodeClassifyInfo.setRecStatus(1);
        leagueNodeClassifyInfo.setDoneDate(new Date());
        leagueNodeClassifyInfoDao.insert(leagueNodeClassifyInfo);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public void addLeagueNodeClassifyInfoForBatch(LeagueNodeClassifyInfo leagueNodeClassifyInfo) throws Exception {
        //批量录入：覆盖信息（即批量导入信息直接覆盖系统内原信息）
        {
            LeagueNodeClassifyInfo leagueNodeClassifyInfoTemp = new LeagueNodeClassifyInfo();
            leagueNodeClassifyInfoTemp.setChannelEntityId(leagueNodeClassifyInfo.getChannelEntityId());
            leagueNodeClassifyInfoTemp.setRecStatus(0);
            leagueNodeClassifyInfoDao.update(leagueNodeClassifyInfoTemp);
        }

        leagueNodeClassifyInfo.setRecStatus(1);
        leagueNodeClassifyInfo.setDoneDate(new Date());
        leagueNodeClassifyInfoDao.insert(leagueNodeClassifyInfo);
    }

    @Override
    public List<List<Object>> batchAddLeagueNodeClassifyInfo(List<List<Object>> list, Long opId, Long orgId) throws Exception {
        List<ChannelSysBaseType> leagueTypeList = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(930013, null, null, null);
        List<ChannelSysBaseType> leagueDistributeList = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(930014, null, null, null);
        String allowLeagueTypes = "";
        for (ChannelSysBaseType channelSysBaseType : leagueTypeList) {
            allowLeagueTypes = allowLeagueTypes + channelSysBaseType.getCodeName() + "、";
        }
        if (allowLeagueTypes.length() > 0) {
            allowLeagueTypes = allowLeagueTypes.substring(0, allowLeagueTypes.length() - 1);
        }

        String allowLeagueDistributes = "";
        for (ChannelSysBaseType channelSysBaseType : leagueDistributeList) {
            allowLeagueDistributes = allowLeagueDistributes + channelSysBaseType.getCodeName() + "、";
        }
        if (allowLeagueDistributes.length() > 0) {
            allowLeagueDistributes = allowLeagueDistributes.substring(0, allowLeagueDistributes.length() - 1);
        }

        List<List<Object>> returnList = new ArrayList<List<Object>>();
        for (int i = 0; i < list.size(); i++) {
            List<Object> row = list.get(i);
            try {
                if (i == 0) {
                    row.add("结果");
                    row.add("备注");
                } else {
                    if (row.size() < 7) {
                        throw new Exception("列数不满足");
                    }

                    String nodeName = row.get(2) == null ? "" : row.get(2).toString();
                    nodeName = nodeName.trim();
                    if (nodeName.equals("")) {
                        throw new Exception("营业厅名称不能为空");
                    }
                    List<Map<String, Object>> nodeInfos = leagueNodeClassifyInfoDao.queryNode(nodeName);
                    if (nodeInfos.size() < 1) {
                        throw new Exception("请确认该营业厅是否存在、是否为加盟营业厅、是否未退出");
                    }
                    if (nodeInfos.size() > 1) {
                        throw new Exception("该营业厅在系统中存在多条，请确认");
                    }
                    Long channelEntityId = Long.valueOf(nodeInfos.get(0).get("CHANNEL_ENTITY_ID").toString());


                    String league_type_name = row.get(5) == null ? "" : row.get(5).toString();
                    league_type_name = league_type_name.trim();
                    if (league_type_name.equals("")) {
                        throw new Exception("营业厅类别 不能为空");
                    }
                    Integer leagueType = -1;
                    for (ChannelSysBaseType channelSysBaseType : leagueTypeList) {
                        if (channelSysBaseType.getCodeName().equals(league_type_name)) {
                            leagueType = channelSysBaseType.getCodeId();
                        }
                    }
                    if (leagueType == -1) {
                        throw new Exception("营业厅类别 无效，只能为：" + allowLeagueTypes);
                    }


                    String league_distribute_name = row.get(6) == null ? "" : row.get(6).toString();
                    league_distribute_name = league_distribute_name.trim();
                    if (league_distribute_name.equals("")) {
                        throw new Exception("营业厅分布 不能为空");
                    }
                    Integer leagueDistribute = -1;
                    for (ChannelSysBaseType channelSysBaseType : leagueDistributeList) {
                        if (channelSysBaseType.getCodeName().equals(league_distribute_name)) {
                            leagueDistribute = channelSysBaseType.getCodeId();
                        }
                    }
                    if (leagueDistribute == -1) {
                        throw new Exception("营业厅分布 无效，只能为：" + allowLeagueDistributes);
                    }


                    LeagueNodeClassifyInfo leagueNodeClassifyInfo = new LeagueNodeClassifyInfo();
                    leagueNodeClassifyInfo.setChannelEntityId(channelEntityId);
                    leagueNodeClassifyInfo.setRecStatus(1);
                    leagueNodeClassifyInfo.setLeagueDistribute(leagueDistribute);
                    leagueNodeClassifyInfo.setLeagueType(leagueType);
                    leagueNodeClassifyInfo.setOpId(opId);
                    leagueNodeClassifyInfo.setOrgId(orgId);
                    leagueNodeClassifyInfo.setDoneDate(new Date());
                    agentServiceFeeService.addLeagueNodeClassifyInfoForBatch(leagueNodeClassifyInfo);

                    row.add("成功");
                    row.add("");
                }
            } catch (Exception e) {
                logger.error("", e);
                while (row.size() < 7) {
                    row.add("");
                }
                row.add("失败");
                row.add(e.getMessage());
            } finally {
                returnList.add(row);
            }
        }

        return returnList;
    }

    @Override
    public void editLeagueNodeClassifyInfo(LeagueNodeClassifyInfo leagueNodeClassifyInfo) throws Exception {
        LeagueNodeClassifyInfo leagueNodeClassifyInfoTemp = new LeagueNodeClassifyInfo();
        leagueNodeClassifyInfoTemp.setChannelEntityId(leagueNodeClassifyInfo.getChannelEntityId());
        leagueNodeClassifyInfoTemp.setRecStatus(0);
        leagueNodeClassifyInfoDao.update(leagueNodeClassifyInfoTemp);


        leagueNodeClassifyInfo.setRecStatus(1);
        leagueNodeClassifyInfoDao.insert(leagueNodeClassifyInfo);
    }

    @Override
    public PageData<LeagueNodeClassifyInfo> queryLeagueNodeClassifyInfo(LeagueNodeClassifyInfo leagueNodeClassifyInfo, PageParameter pageParameter) {
        List<LeagueNodeClassifyInfo> leagueNodeClassifyInfoList = leagueNodeClassifyInfoDao.queryLeagueNodeClassifyInfo(leagueNodeClassifyInfo, pageParameter);
        return new PageData<LeagueNodeClassifyInfo>(leagueNodeClassifyInfoList, pageParameter);
    }

    @Override
    public PageData<ChannelFuseItem> queryChannelFuseItemInfo(ChannelFuseItem channelFuseItem, PageParameter pageParameter) {
        List<ChannelFuseItem> channelFuseItemList =null;
        try {
            channelFuseItemList = channelFuseItemDao.queryChannelFuseItemInfo(channelFuseItem, pageParameter);
            for (ChannelFuseItem entity:channelFuseItemList) {
                if (StringUtils.isNotNullOrBlank(entity.getBillMonth()))
                    entity.setDisCountMonth(entity.getBillMonth().substring(0,7));
            }
        }catch (Exception e){
            logger.info(""+e);
        }

        return new PageData<ChannelFuseItem>(channelFuseItemList, pageParameter);
    }

    @Override
    public PageData<ChannelFuseedRigidTarget> queryChannelFuseedRigidTargetInfo(ChannelFuseedRigidTarget channelFuseedRigidTarget, PageParameter pageParameter) {
        List<ChannelFuseedRigidTarget> channelFuseedRigidTargetList =null;
        String isFinish = channelFuseedRigidTarget.getIsFinish();
        if (StringUtils.isNotNullOrBlank(isFinish)){
            if (isFinish.equals("0")){
                channelFuseedRigidTarget.setIsFinish("否");
            }else {
                channelFuseedRigidTarget.setIsFinish("是");
            }
        }
        try {
            channelFuseedRigidTargetList = channelFuseedRigidTargetDao.queryChannelFuseedRigidTargetInfo(channelFuseedRigidTarget, pageParameter);
        }catch (Exception e){
            logger.info(""+e);
        }

        return new PageData<ChannelFuseedRigidTarget>(channelFuseedRigidTargetList, pageParameter);
    }

    @Override
    public void delLeagueNodeClassifyInfo(LeagueNodeClassifyInfo leagueNodeClassifyInfo) throws Exception {
        leagueNodeClassifyInfo.setRecStatus(0);
        leagueNodeClassifyInfoDao.update(leagueNodeClassifyInfo);
    }

    @Override
    public void delChannelFuseItemInfo(ChannelFuseItem channelFuseItem) throws Exception {
        channelFuseItem.setStatus(0);
        channelFuseItemDao.update(channelFuseItem);
    }

    @Override
    public void companyScoresSave(String channelEntityName, CompanyAssessmentScore companyAssessmentScore) throws Exception {
        ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
        channelEntityBasicInfo.setChannelEntityName(channelEntityName);
        Long channelEntityId = null;
        List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.queryChannelEntityBasicInfo(channelEntityBasicInfo);
        if ((!CollectionUtils.isEmpty(channelEntityBasicInfoList)) && (channelEntityBasicInfoList.size() == 1)) {
            channelEntityId = channelEntityBasicInfoList.get(0).getChannelEntityId();
            companyAssessmentScore.setChannelEntityId(channelEntityId);
            companyAssessmentScoreDao.insert(companyAssessmentScore);
        }
    }

    @Override
    public void companyScoresModify(String channelEntityName, CompanyAssessmentScore companyAssessmentScore) throws Exception {
        ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
        channelEntityBasicInfo.setChannelEntityName(channelEntityName);
        Long channelEntityId = null;
        List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.queryChannelEntityBasicInfo(channelEntityBasicInfo);
        if ((!CollectionUtils.isEmpty(channelEntityBasicInfoList)) && (channelEntityBasicInfoList.size() == 1)) {
            channelEntityId = channelEntityBasicInfoList.get(0).getChannelEntityId();
            companyAssessmentScore.setChannelEntityId(channelEntityId);

            CompanyAssessmentScore queryParameter = new CompanyAssessmentScore();
            queryParameter.setBillMonth(companyAssessmentScore.getBillMonth());
            queryParameter.setChannelEntityId(channelEntityId);
            List<CompanyAssessmentScore> companyAssessmentScoreList = companyAssessmentScoreDao.query(queryParameter);
            if ((!CollectionUtils.isEmpty(companyAssessmentScoreList)) && companyAssessmentScoreList.size() == 1) {
                CompanyAssessmentScore companyAssessmentScore1 = companyAssessmentScoreList.get(0);
                companyAssessmentScore1.setDoneDate(new Date());
                companyAssessmentScore1.setRecStatus(0);
                //先将原来的记录置为失效
                companyAssessmentScoreDao.update(companyAssessmentScore1);
                companyAssessmentScoreDao.insert(companyAssessmentScore);
            }

        }

    }


    @Override
    public Boolean checkScoreIsExist(String channelEntityName, CompanyAssessmentScore companyAssessmentScore) throws Exception {
        Boolean flag = false;
        ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
        channelEntityBasicInfo.setChannelEntityName(channelEntityName);
        String billMonth=companyAssessmentScore.getBillMonth();
        Long channelEntityId = null;
        List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.queryChannelEntityBasicInfo(channelEntityBasicInfo);
        if ((!CollectionUtils.isEmpty(channelEntityBasicInfoList)) && (channelEntityBasicInfoList.size() == 1)) {
            channelEntityId = channelEntityBasicInfoList.get(0).getChannelEntityId();
            CompanyAssessmentScore queryParameter = new CompanyAssessmentScore();
            queryParameter.setBillMonth(companyAssessmentScore.getBillMonth());
            queryParameter.setChannelEntityId(channelEntityId);
            List<CompanyAssessmentScore> companyAssessmentScoreList = companyAssessmentScoreDao.query(queryParameter);
            if ((!CollectionUtils.isEmpty(companyAssessmentScoreList)) && companyAssessmentScoreList.size() > 0) {
                flag = true;
            }else{
                flag=false;
            }
        } else {
            throw new Exception("代理商中移在线的相关信息不存在或存在多条");
        }
        return flag;
    }

    @Override
    public PageData<CompanyAssessmentScore> queryAssessmentScore(CompanyAssessmentScore companyAssessmentScore, PageParameter page) throws Exception {
        List<CompanyAssessmentScore> companyAssessmentScoreList = companyAssessmentScoreDao.queryAssessmentScore(companyAssessmentScore, page);
        return new PageData<CompanyAssessmentScore>(companyAssessmentScoreList, page);
    }

    @Override
    public List<CompanyAssessmentScore> queryDetailScoreInfo(CompanyAssessmentScore companyAssessmentScore) throws Exception {
        List<CompanyAssessmentScore> companyAssessmentScoreList = companyAssessmentScoreDao.query(companyAssessmentScore);
        return companyAssessmentScoreList;
    }

    @Override
    public Boolean checkTietongScoreIsExist(String channelEntityName, TietongItemScore tietongItemScore) throws Exception {
        Boolean flag = false;
        ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
        channelEntityBasicInfo.setChannelEntityName(channelEntityName);
        String billMonth=tietongItemScore.getBillMonth();
        Long channelEntityId = null;
        List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.queryChannelEntityBasicInfo(channelEntityBasicInfo);
        if ((!CollectionUtils.isEmpty(channelEntityBasicInfoList))) {
            channelEntityId = channelEntityBasicInfoList.get(0).getChannelEntityId();
            TietongItemScore queryParameter = new TietongItemScore();
            queryParameter.setBillMonth(tietongItemScore.getBillMonth());
            queryParameter.setChannelEntityId(channelEntityId);
            List<TietongItemScore> TietongItemScoreList = tietongItemScoreDao.queryList(queryParameter);
            if ((!CollectionUtils.isEmpty(TietongItemScoreList)) && TietongItemScoreList.size() > 0) {
                flag = true;
            }else{
                flag=false;
            }
        } else {
            throw new Exception("代理商铁通的相关信息不存在");
        }
        return flag;
    }

    @Override
    public void tietongScoresSave(String channelEntityName, TietongItemScore tietongItemScore) throws Exception {
        ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
        channelEntityBasicInfo.setChannelEntityName(channelEntityName);
        Long channelEntityId = null;
        List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.queryChannelEntityBasicInfo(channelEntityBasicInfo);
        if ((!CollectionUtils.isEmpty(channelEntityBasicInfoList)) && (channelEntityBasicInfoList.size() == 1)) {
            channelEntityId = channelEntityBasicInfoList.get(0).getChannelEntityId();
            tietongItemScore.setChannelEntityId(channelEntityId);
            tietongItemScore.setChannelEntityName(channelEntityName);
            tietongItemScoreDao.insert(tietongItemScore);
        }

    }
    @Override
    public void companySpecSettlementAdd(CompanySpecSettlement companySpecSettlement) throws Exception{
        Long doneCode = companySpecSettlementDao.getSequence();
        try {
            companySpecSettlement.setDoneCode(doneCode);
            companySpecSettlementDao.insert(companySpecSettlement);
        }catch (Exception e){
            throw new Exception(e);
        }
    }
    @Override
    public PageData queryTietongScore(TietongItemScore tietongItemScore, PageParameter page) throws Exception {
        List<TietongItemScore> tietongItemScoreList = tietongItemScoreDao.queryTietongScore(tietongItemScore, page);
        return new PageData<TietongItemScore>(tietongItemScoreList, page);
    }

    @Override
    public List<TietongItemScore> queryTietongDetailScoreInfo(TietongItemScore tietongItemScore) throws Exception {
        List<TietongItemScore> tietongItemScoreList = tietongItemScoreDao.queryList(tietongItemScore);
        return tietongItemScoreList;
    }

    @Override
    public void tietongScoresModify(String channelEntityName, TietongItemScore tietongItemScore) throws Exception {
        ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
        channelEntityBasicInfo.setChannelEntityName(channelEntityName);
        Long channelEntityId = null;
        List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.queryChannelEntityBasicInfo(channelEntityBasicInfo);
        if ((!CollectionUtils.isEmpty(channelEntityBasicInfoList)) && (channelEntityBasicInfoList.size() == 1)) {
            channelEntityId = channelEntityBasicInfoList.get(0).getChannelEntityId();
            tietongItemScore.setChannelEntityId(channelEntityId);

            TietongItemScore queryParameter = new TietongItemScore();
            queryParameter.setBillMonth(tietongItemScore.getBillMonth());
            queryParameter.setChannelEntityId(channelEntityId);
            List<TietongItemScore> tietongItemScoreList = tietongItemScoreDao.queryList(queryParameter);
            if ((!CollectionUtils.isEmpty(tietongItemScoreList)) && tietongItemScoreList.size() == 1) {
                TietongItemScore tietongItemScore1 = tietongItemScoreList.get(0);
                tietongItemScore1.setDoneDate(new Date());
                tietongItemScore1.setRecStatus(0);
                //先将原来的记录置为失效
                tietongItemScoreDao.update(tietongItemScore1);
                tietongItemScore.setChannelEntityName(channelEntityName);
                tietongItemScoreDao.insert(tietongItemScore);
            }

        }
    }

    @Override
    public Boolean checkContentIsExist(String channelEntityName, CheckContentScore checkContentScore) throws Exception {
        Boolean flag = false;
        ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
        channelEntityBasicInfo.setChannelEntityName(channelEntityName);
        Long channelEntityId = null;
        List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.queryChannelEntityBasicInfo(channelEntityBasicInfo);
        if ((!CollectionUtils.isEmpty(channelEntityBasicInfoList)) && (channelEntityBasicInfoList.size() == 1)) {
            channelEntityId = channelEntityBasicInfoList.get(0).getChannelEntityId();
            CheckContentScore queryParameter = new CheckContentScore();
            queryParameter.setBillMonth(checkContentScore.getBillMonth());
            queryParameter.setChannelEntityId(channelEntityId);
            List<CheckContentScore> checkContentScores = checkContentScoreDao.queryList(queryParameter);
            if ((!CollectionUtils.isEmpty(checkContentScores)) && checkContentScores.size() > 0) {
                flag = true;
            } else {
                flag = false;
            }
        } else {
            throw new Exception("代理商中移在线的相关信息不存在或存在多条");
        }
        return flag;
    }

    @Override
    public void checkContentScoreSave(String channelEntityName, CheckContentScore checkContentScore) throws Exception {
        ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
        channelEntityBasicInfo.setChannelEntityName(channelEntityName);
        Long channelEntityId = null;
        List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.queryChannelEntityBasicInfo(channelEntityBasicInfo);
        if ((!CollectionUtils.isEmpty(channelEntityBasicInfoList)) && (channelEntityBasicInfoList.size() == 1)) {
            channelEntityId = channelEntityBasicInfoList.get(0).getChannelEntityId();
            checkContentScore.setChannelEntityId(channelEntityId);
            checkContentScore.setChannelEntityName(channelEntityName);
            checkContentScoreDao.insert(checkContentScore);
        }
    }

    @Override
    public PageData queryCheckContentScore(CheckContentScore checkContentScore, PageParameter page) throws Exception {
        List<CheckContentScore> checkContentScoreList = checkContentScoreDao.queryCheckContentScore(checkContentScore, page);
        return new PageData<CheckContentScore>(checkContentScoreList, page);
    }

    @Override
    public List<CheckContentScore> queryCheckContentDetailScoreInfo(CheckContentScore checkContentScore) throws Exception {
        List<CheckContentScore> checkContentScoreList = checkContentScoreDao.queryList(checkContentScore);
        return checkContentScoreList;
    }

    @Override
    public void checkContentModify(String channelEntityName, CheckContentScore checkContentScore) throws Exception {
        ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
        channelEntityBasicInfo.setChannelEntityName(channelEntityName);
        Long channelEntityId = null;
        List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.queryChannelEntityBasicInfo(channelEntityBasicInfo);
        if ((!CollectionUtils.isEmpty(channelEntityBasicInfoList)) && (channelEntityBasicInfoList.size() == 1)) {
            channelEntityId = channelEntityBasicInfoList.get(0).getChannelEntityId();
            checkContentScore.setChannelEntityId(channelEntityId);

            CheckContentScore queryParameter = new CheckContentScore();
            queryParameter.setBillMonth(checkContentScore.getBillMonth());
            queryParameter.setChannelEntityId(channelEntityId);
            List<CheckContentScore> checkContentScoresList = checkContentScoreDao.queryList(queryParameter);
            if ((!CollectionUtils.isEmpty(checkContentScoresList)) && checkContentScoresList.size() == 1) {
                CheckContentScore checkContentScore1 = checkContentScoresList.get(0);
                checkContentScore1.setDoneDate(new Date());
                checkContentScore1.setRecStatus(0);
                //先将原来的记录置为失效
                checkContentScoreDao.update(checkContentScore1);
                checkContentScore.setChannelEntityName(channelEntityName);
                checkContentScoreDao.insert(checkContentScore);
            }

        }
    }

    @Override
    public List<ChannelSysBaseType> queryCodeName(String channelEntityName) throws Exception {
        List<ChannelSysBaseType> channelSysBaseTypeList = checkContentScoreDao.queryCodeName(channelEntityName);
        return channelSysBaseTypeList;
    }

    @Override
    public void resetRecStatus(String billMonth) throws Exception {
        try {
            companyAssessmentScoreDao.resetRecStatus(0,billMonth);
        }catch (Exception e){
            logger.error("将在线公司已录入上传数据置为0失败",e);
        }
    }

    @Override
    public void resetRecStatus2(String billMonth) throws Exception {
        try {
            tietongItemScoreDao.resetRecStatus(0,billMonth);
        }catch (Exception e){
            logger.error("将铁通看管项目已录入上传数据置为0失败",e);
        }
    }

    @Override
    public String getRemoteFilePath(String billMonth) throws Exception {
        String remoteFilePath="";
        try {
            remoteFilePath=companyAssessmentScoreDao.getRemoteFilePath(billMonth);
        }catch (Exception e){
            logger.error("查询在线公司上传的文件名称失败",e);
        }
        return remoteFilePath;
    }

    @Override
    public String getRemoteFilePath2(String billMonth) throws Exception {
        String remoteFilePath="";
        try {
            remoteFilePath=tietongItemScoreDao.getRemoteFilePath2(billMonth);
        }catch (Exception e){
            logger.error("查询铁通看管项目上传的文件名称失败",e);
        }
        return remoteFilePath;
    }

    @Override
    public PageData<BusinessThirdProjectRatio> queryBusinessThirdProjectRatio(BusinessThirdProjectRatio businessThirdProjectRatio, PageParameter page) throws Exception {
        List<BusinessThirdProjectRatio> businessThirdSupportFeeList = null;
        if (StringUtils.isNotNullOrBlank(businessThirdProjectRatio.getChannelTeam())) {
            //渠道团队枚举转换
            List<ChannelSysBaseType> channelSysBaseTypeList =
                    ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10054, Integer.parseInt(businessThirdProjectRatio.getChannelTeam()), 1, null);
            if (channelSysBaseTypeList != null) {
                businessThirdProjectRatio.setChannelTeam(channelSysBaseTypeList.get(0).getCodeName());
            }
        }
        try {
            businessThirdSupportFeeList = businessThirdProjectRatioDao.queryBusinessThirdProjectRatio(businessThirdProjectRatio,page);
        }catch (Exception e){
            throw new Exception(e);
        }

        return new PageData<BusinessThirdProjectRatio>(businessThirdSupportFeeList,page);
    }

    @Override
    public void addBusinessThirdProjectRatio(BusinessThirdProjectRatio businessThirdProjectRatio) throws Exception{
        //渠道团队枚举转换
        List<ChannelSysBaseType> channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10054, Integer.parseInt(businessThirdProjectRatio.getChannelTeam()), 1, null);
        if (channelSysBaseTypeList != null){
            businessThirdProjectRatio.setChannelTeam(channelSysBaseTypeList.get(0).getCodeName());
        }
        //判断记录是否存在
        List<BusinessThirdProjectRatio> businessThirdProjectRatioList = businessThirdProjectRatioDao.query(businessThirdProjectRatio);
        if(businessThirdProjectRatioList.size() > 0){
            throw new Exception("该渠道团队月度目标系数已录入，请至第三方行销项目月度目标系数管理页面进行修改。");
        }
        businessThirdProjectRatio.setRecStatus(1);
        businessThirdProjectRatio.setDoneDate(new Date());
        businessThirdProjectRatioDao.insert(businessThirdProjectRatio);

    }

    @Override
    public void delBusinessThirdProjectRatio(BusinessThirdProjectRatio businessThirdProjectRatio) throws Exception{
        try {
            //渠道团队枚举转换
            List<ChannelSysBaseType> channelSysBaseTypeList =
                    ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10054, Integer.parseInt(businessThirdProjectRatio.getChannelTeam()), 1, null);
            if (channelSysBaseTypeList != null){
                businessThirdProjectRatio.setChannelTeam(channelSysBaseTypeList.get(0).getCodeName());
            }
            businessThirdProjectRatio.setRecStatus(0);
            businessThirdProjectRatioDao.update(businessThirdProjectRatio);
        }catch (Exception e){
            throw new Exception(e);
        }


    }

    @Override
    public void editBusinessThirdProjectRatio(BusinessThirdProjectRatio businessThirdProjectRatio) throws Exception {
        try {
            //渠道团队枚举转换
            List<ChannelSysBaseType> channelSysBaseTypeList =
                    ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10054, Integer.parseInt(businessThirdProjectRatio.getChannelTeam()), 1, null);
            if (channelSysBaseTypeList != null){
                businessThirdProjectRatio.setChannelTeam(channelSysBaseTypeList.get(0).getCodeName());
            }
            BusinessThirdProjectRatio businessThirdProjectRatioTemp = new BusinessThirdProjectRatio();

            businessThirdProjectRatioTemp.setBillMonth(businessThirdProjectRatio.getBillMonth());
            businessThirdProjectRatioTemp.setChannelTeam(businessThirdProjectRatio.getChannelTeam());
            businessThirdProjectRatioTemp.setRecStatus(0);
            businessThirdProjectRatioTemp.setDoneDate(new Date());
            businessThirdProjectRatioDao.update(businessThirdProjectRatioTemp);
            businessThirdProjectRatio.setRecStatus(1);
            businessThirdProjectRatio.setDoneDate(new Date());
            businessThirdProjectRatioDao.insert(businessThirdProjectRatio);
        }catch (Exception e){
            throw new Exception(e);
        }

    }

    @Override
    public PageData<BusinessThirdProjectReach> queryBusinessThirdProjectReach(BusinessThirdProjectReach businessThirdProjectReach, PageParameter page) throws Exception {
        List<BusinessThirdProjectReach> businessThirdProjectReachList = null;
        try {
            businessThirdProjectReachList = businessThirdProjectReachDao.queryBusinessThirdProjectReach(businessThirdProjectReach,page);
        }catch (Exception e){
            throw new Exception(e);
        }

        return new PageData<BusinessThirdProjectReach>(businessThirdProjectReachList,page);
    }

    @Override
    public void editBusinessThirdProjectReach(BusinessThirdProjectReach businessThirdProjectReach) throws Exception {
        try {
            BusinessThirdProjectReach businessThirdProjectReachtemp = new BusinessThirdProjectReach();
            businessThirdProjectReachtemp.setDoneCode(businessThirdProjectReach.getDoneCode());
            businessThirdProjectReachtemp.setRecStatus(0);
            businessThirdProjectReachtemp.setDoneDate(new Date());
            businessThirdProjectReachDao.update(businessThirdProjectReachtemp);
            businessThirdProjectReach.setRecStatus(1);
            businessThirdProjectReach.setDoneDate(new Date());
            businessThirdProjectReachDao.insert(businessThirdProjectReach);
        }catch (Exception e){
            throw new Exception(e);
        }

    }


    @Override
    public List<List<Object>> channelFuseItemInfo(List<List<Object>> list, String remoteFileName, String remotePath, SPrivData sPrivData) throws Exception {
        List<List<Object>> returnList = new ArrayList<List<Object>>();
        for (int i = 0; i < list.size(); i++) {
            List<Object> row = list.get(i);
            try {
                if (i == 0) {
                    row.add("结果");
                    row.add("备注");
                } else {
                    if (row.size() != 1) {
                        throw new Exception("列数不满足");
                    }

                    String nodeName = row.get(0) == null ? "" : row.get(0).toString();
                    nodeName = nodeName.trim();
                    if (nodeName.equals("")) {
                        throw new Exception("营业厅名称不能为空");
                    }
                    List<Map<String, Object>> nodeInfos = channelFuseItemDao.queryNodeByName(nodeName);
                    if (nodeInfos.size() < 1) {
                        throw new Exception("请确认该营业厅是否存在、状态是否正常");
                    }
                    if (nodeInfos.size() > 1) {
                        throw new Exception("该营业厅在系统中存在多条，请确认");
                    }
                    Long channelEntityId = Long.valueOf(nodeInfos.get(0).get("CHANNEL_ENTITY_ID").toString());
                    Long orgId =sPrivData.getOrgId();
                    String orgName= sPrivData.getOrgName();

                    ChannelFuseItem channelFuseItem = new ChannelFuseItem();
                    channelFuseItem.setChannelEntityId(channelEntityId);
                    channelFuseItem.setChannelEntityName(nodeName);
                    channelFuseItem.setOrgId(orgId);
                    channelFuseItem.setOrgName(orgName);
                    channelFuseItem.setBillMonth(DateUtil.formatDate(new Date(),"yyyy/MM/dd"));
                    channelFuseItem.setStatus(1);
                    channelFuseItem.setFileName(remoteFileName);
                    channelFuseItem.setFilePath(remotePath);
                    agentServiceFeeService.addChannelFuseItemInfoForBatch(channelFuseItem);
                    row.add("成功");
                    row.add("");
                }
            } catch (Exception e) {
                logger.error("", e);
                while (row.size() < 1) {
                    row.add("");
                }
                row.add("失败");
                row.add(e.getMessage());
            } finally {
                returnList.add(row);
            }
        }

        return returnList;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public void addChannelFuseItemInfoForBatch(ChannelFuseItem channelFuseItem) throws Exception {
        //批量录入：覆盖信息（即批量导入信息直接覆盖系统内原信息）
        {
            ChannelFuseItem channelFuseItemInfo = new ChannelFuseItem();
            channelFuseItemInfo.setChannelEntityId(channelFuseItem.getChannelEntityId());
            channelFuseItemInfo.setStatus(0);
            channelFuseItemDao.update(channelFuseItemInfo);
        }

        channelFuseItemDao.insert(channelFuseItem);
    }

    @Override
    public List<List<Object>> channelFuseedRigidTargetInfo(List<List<Object>> list, SPrivData sPrivData) throws Exception {
        List<List<Object>> returnList = new ArrayList<List<Object>>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyydd");
        Long orgId =sPrivData.getOrgId();
        String orgName= sPrivData.getOrgName();
        Long opId = sPrivData.getOpId();
        for (int i = 0; i < list.size(); i++) {
            List<Object> row = list.get(i);
            try {
                if (i == 0) {
                    row.add("结果");
                    row.add("备注");
                } else {
                    if (row.size() != 2) {
                        throw new Exception("列数不满足");
                    }

                    Date date = new Date();
                    SimpleDateFormat sdf2 =new SimpleDateFormat("yyyyMM");
                    Calendar cal=Calendar.getInstance();
                    cal.setTime(date);
                    cal.add(Calendar.MONTH,-1);
                    String billMonth=sdf2.format(cal.getTime());
                    ChannelFuseedRigidTarget cfrt = new ChannelFuseedRigidTarget();
                    cfrt.setBillMonth(billMonth);
//                    if (StringUtils.isNullOrBlank(billMonth)){
//                        throw new Exception("融合刚性目标 月份 字段为必填，请重新录入！！！");
//                    }else{
//                        //转换字符串为时间格式 YYYYMM 如果转换失败就拦截 代表批量的时间格式不正确
//                        try {
//                            sdf.parse(billMonth);
//                        }catch (Exception e){
//                            throw new Exception("请正确填写 融合刚性目标 该字段只能输入的时间格式为 YYYYMM");
//                        }
//                    }
                    cfrt.setOrgId(orgId);
//                    List<ChannelFuseedRigidTarget> channelFuseedRigidTargets =channelFuseedRigidTargetDao.query(cfrt);
//                    if (channelFuseedRigidTargets.size() != 0){
//                        throw new Exception("当月该所属分公司信息已录入，请勿重复录入！！！");
//                    }
                    String channelEntityName = row.get(0) == null ? "" : row.get(0).toString();
                    if (StringUtils.isNullOrBlank(channelEntityName)){
                        throw new Exception("网点名称不能为空");
                    }
                    List<Map<String, Object>> nodeInfos = channelFuseItemDao.queryNodeByName(channelEntityName);
                    if (nodeInfos.size() < 1) {
                        throw new Exception("请确认该网点名称是否存在、状态是否正常");
                    }
                    if (nodeInfos.size() > 1) {
                        throw new Exception("该网点名称在系统中存在多条，请确认");
                    }
                    //判断网点
                    List<Map<String, Object>> nodeInfos2 = channelFuseedRigidTargetDao.queryNodeAndMonth(channelEntityName,billMonth);
                    if (nodeInfos2.size() > 0) {
                        throw new Exception("该网点本月已经录入！");
                    }


                    String isFinish = row.get(1) == null ? "" : row.get(1).toString();
                    if (StringUtils.isNotNullOrBlank(isFinish)){
                        if (isFinish.equals("是") && isFinish.equals("否")){
                            throw new Exception("请正确填写 融合刚性目标 是否完成 该字段只能输入“是/否");
                        }
                    }else{
                        throw new Exception("融合刚性目标 是否完成 字段为必填，请重新录入！！！");
                    }
                    cfrt.setOpId(opId);
                    cfrt.setOrgId(orgId);
                    cfrt.setOrgName(orgName);
                    cfrt.setBillMonth(billMonth);
                    cfrt.setChannelEntityName(channelEntityName);
                    cfrt.setDoneDate(new Date());
                    cfrt.setIsFinish(isFinish);
                    cfrt.setStatus(1);
                    channelFuseedRigidTargetDao.insert(cfrt);
                    row.add("成功");
                    row.add("");
                }
            } catch (Exception e) {
                logger.error("", e);
                while (row.size() < 1) {
                    row.add("");
                }
                row.add("失败");
                row.add(e.getMessage());
            } finally {
                returnList.add(row);
            }
        }

        return returnList;
    }

    @Override
    public void editChannelFuseedRigidTarget(ChannelFuseedRigidTarget channelFuseedRigidTarget) throws Exception {
        try {
            channelFuseedRigidTarget.setStatus(0);
            channelFuseedRigidTargetDao.update(channelFuseedRigidTarget);

            channelFuseedRigidTarget.setStatus(1);
            channelFuseedRigidTarget.setDoneDate(new Date());
            channelFuseedRigidTargetDao.insert(channelFuseedRigidTarget);
        }catch (Exception e){
            throw new Exception("修改数据失败，原因是：" +e);
        }

    }


    @Override
    public PageData<BusinessThirdPersonMarket> queryBusinessThirdPersonMarket(BusinessThirdPersonMarket businessThirdPersonMarket, PageParameter page) throws Exception {
        List<BusinessThirdPersonMarket> businessThirdPersonMarketList = null;
        try {
            businessThirdPersonMarketList = businessThirdPersonMarketDao.queryBusinessThirdPersonMarketInfo(businessThirdPersonMarket,page);
        }catch (Exception e){
            throw new Exception(e);
        }

        return new PageData<BusinessThirdPersonMarket>(businessThirdPersonMarketList,page);
    }

    @Override
    public void editBusinessThirdPersonMarket(BusinessThirdPersonMarket businessThirdPersonMarket) throws Exception {
        try {
            BusinessThirdPersonMarket businessThirdPersonMarketTemp = businessThirdPersonMarket;

            businessThirdPersonMarketTemp.setRecStatus(0);
            businessThirdPersonMarketTemp.setDoneDate(new Date());
            businessThirdPersonMarketDao.update(businessThirdPersonMarketTemp);
            businessThirdPersonMarket.setRecStatus(1);
            businessThirdPersonMarket.setDoneDate(new Date());
            businessThirdPersonMarketDao.insert(businessThirdPersonMarket);
        }catch (Exception e){
            throw new Exception(e);
        }

    }
    @Override
    public List<List<Object>> busiThirdXXAssessInfo(List<List<Object>> list,
                                                    Long agentAdjustUseId,
                                                    String remoteFileName,
                                                    String remotePath,
                                                    SPrivData sPrivData) throws Exception {
        List<List<Object>> returnList = new ArrayList<List<Object>>();

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -1);
        String billMonthMinus1 = new SimpleDateFormat("yyyyMM").format(cal.getTime());

        Calendar cal2 = Calendar.getInstance();
        cal2.add(Calendar.MONTH, -2);
        String billMonthMinus2 = new SimpleDateFormat("yyyyMM").format(cal2.getTime());

        Long orgId = sPrivData.getOrgId();
        String orgName = sPrivData.getOrgName();
        Long opId = sPrivData.getOpId();

        // 清理过期数据
        BusiThirdXXAssess busiThirdXXAssessTemp = new BusiThirdXXAssess();
        busiThirdXXAssessTemp.setOrgId(orgId);
        busiThirdXXAssessTemp.setBillMonth(billMonthMinus1);
        busiThirdXXAssessTemp.setRecStatus(-1);
        busiThirdXXAssessTemp.setBillDate("1");
        busiThirdXXAssessDao.update(busiThirdXXAssessTemp);

        // 获取序列号
        Long doneCode = busiThirdXXAssessDao.getSequence();

        //[0,10] [0,90]整数 正则校验
        String reg1 = "^(0|([1-9]|10))$";
        String reg2 = "^(0|([1-8]?[0-9]|90))$";
        String reg3 = "^-?\\d+(\\.\\d{1,2})?$";
        String reg4 = "^(?:100(?:\\.0{1,2})?|(?:[1-9]?\\d|100)(?:\\.\\d{1,2})?)$";
        Double totalFee = 0.00;
        Double basicTotalFee = 0.00;
        Integer regionId = null;

        for (int i = 0; i < list.size(); i++) {
            List<Object> row = list.get(i);
            if (row == null) continue;

            try {
                if (i == 0) {
                    row.add("结果");
                    row.add("备注");
                } else {
                    if (row.size() != 6) {
                        throw new Exception("列数不满足");
                    }
//                    String channelTeam = row.get(0) == null ? "" : row.get(0).toString();
                    String roleNameText = row.get(0) == null ? "" : row.get(0).toString();
                    long roleName = ChannelSysBaseTypeUtil.getCodeId(50468, roleNameText.trim());
                    String nodeName = row.get(1) == null ? "" : row.get(1).toString().trim();
                    /*if (StringUtils.isNullOrBlank(channelTeam)){
                        throw new Exception("团队信息不能为空");
                    }
                    if (!channelTeam.equals("第三方直销人员")){
                        throw new Exception("团队字段必须为‘第三方直销人员’");
                    }*/
                    if (roleName == 0L) {
                        throw new Exception("角色不能为空");
                    }
                    if (roleName != 1L && roleName != 2L && roleName != 3L && roleName != 4L) {
                        throw new Exception("团队字段必须为‘高级社区经理','初级社区经理','商客经理'");
                    }
                    if (nodeName.equals("")) {
                        throw new Exception("网点工号名称不能为空");
                    }

                    List<Map<String, Object>> nodeInfos = channelFuseItemDao.queryNodeByName(nodeName);
                    if (nodeInfos.size() < 1) {
                        throw new Exception("请确认该网点工号名称是否存在、状态是否正常");
                    }
                    if (nodeInfos.size() > 1) {
                        throw new Exception("该网点工号名称在系统中存在多条，请确认");
                    }

                    BusiThirdSalesRoleUpd busiThirdSalesRoleUpd = new BusiThirdSalesRoleUpd();
                    busiThirdSalesRoleUpd.setBillMonth(billMonthMinus2);
                    busiThirdSalesRoleUpd.setNodeName(nodeName);
                    busiThirdSalesRoleUpd.setAuditStatus(3);
                    busiThirdSalesRoleUpd.setRecStatus(1);
                    List<BusiThirdSalesRoleUpd> busiThirdSalesRoleUpds =  busiThirdSalesRoleUpdDao.query(busiThirdSalesRoleUpd);

                    if (busiThirdSalesRoleUpds.size() > 0){
                        Long lastMonthRole = (long)busiThirdSalesRoleUpds.get(0).getRoleId();
                        if (lastMonthRole != roleName) {
                            throw new Exception("上传附件中" + nodeName + "的角色与上月底系统角色不一致，请修改后提交");
                        }
                    }else {
                        Long busiRole = Long.valueOf(nodeInfos.get(0).get("BUSI_ROLE").toString());
                        if (busiRole != roleName) {
                            throw new Exception("上传附件中" + nodeName + "的角色与系统不一致，请修改后提交");
                        }
                    }
                    
                    
                    // 因为工具类原因会把输入的得分转Double 然后转String
                    /*
                    String assesmentScore = row.get(2) == null ? "" : row.get(2).toString().trim();
                    String assesTTScore = row.get(3) == null ? "" : row.get(3).toString().trim();
                    String negativePenaltyScore = row.get(4) == null ? "" : row.get(4).toString().trim();
                    if (StringUtils.isNullOrBlank(assesmentScore)){
                        throw new Exception("行销考核得分不能为空");
                    }else{
                        assesmentScore = String.valueOf(((Double) row.get(2)).intValue());
                    }
                    if (StringUtils.isNullOrBlank(assesTTScore)){
                        throw new Exception("行销人员铁通考核得分不能为空");
                    }else{
                        assesTTScore = String.valueOf(((Double) row.get(3)).intValue());
                    }
                    if (StringUtils.isNullOrBlank(negativePenaltyScore)){
                        throw new Exception("负向扣罚得分不能为空");
                    }else{
                        negativePenaltyScore = String.valueOf(((Double) row.get(4)).intValue());
                    }
                    if (!assesmentScore.matches(reg2)){
                        throw new Exception("当月移动属地考核得分只能输入【0，90】的整数，请确认");
                    }
                    if (!assesTTScore.matches(reg1)){
                        throw new Exception("当月铁通考核得分只能输入【0，10】的整数，请确认");
                    }
                    if (!negativePenaltyScore.matches(reg1)){
                        throw new Exception("当月负向扣罚得分只能输入【0，10】的整数，请确认");
                    }
                    */
                    String billMonthScore = row.get(2) == null ? "" : row.get(2).toString().trim();
                    String assesGridScore = row.get(3) == null ? "" : row.get(3).toString().trim();
                    String basicCostFee = row.get(4) == null ? "" : row.get(4).toString().trim();
                    String marketAdjustFee = row.get(5) == null ? "" : row.get(5).toString().trim();
                    if (roleName != 3) {
                        if (Double.parseDouble(basicCostFee) != 0){
                            throw new Exception("非高级社区经理的基本费用分配只能只能录入0，请重新输入！！");
                        }
                    }

                    if (StringUtils.isNullOrBlank(billMonthScore)) {
                        throw new Exception("当月考核得分不能为空");
                    } else {
                        billMonthScore = String.valueOf(Double.parseDouble(row.get(2).toString()));
                        if (!billMonthScore.matches(reg4)) {
                            throw new Exception("当月考核得分只能输入【0，100】且最多支持两位小数，请确认");
                        }
//                        if (Double.parseDouble(basicCostFee) * Double.parseDouble(row.get(2).toString())/100 > 6360) {
//                            throw new Exception("单个网点的当月考核得分*基本费用分配不能超过6360元，请重新输入！！");
//                        }
                    }
                    if (StringUtils.isNullOrBlank(assesGridScore)) {
                        throw new Exception("其中：网格长考核打分不能为空");
                    } else {
                        assesGridScore = String.valueOf(Double.parseDouble(row.get(3).toString()));
                        if (!assesGridScore.matches(reg4)) {
                            throw new Exception("其中：网格长考核打分只能输入【0，100】且最多支持两位小数，请确认");
                        }
                    }
                    if (StringUtils.isNullOrBlank(basicCostFee) && roleName == 3L) {
                        throw new Exception("基本费用分配不能为空");
                    }
                    if (StringUtils.isNotNullOrBlank(basicCostFee)) {
                        basicCostFee = String.valueOf(Double.parseDouble(row.get(4).toString()));
                        if (!basicCostFee.matches(reg3)) {
                            throw new Exception("基本费用分配最多支持两位小数，请确认");
                        }
                    }
                    if (StringUtils.isNullOrBlank(marketAdjustFee)) {
                        throw new Exception("营销费用调整费不能为空");
                    } else {
                        marketAdjustFee = String.valueOf(Double.parseDouble(row.get(5).toString()));
                        if (!marketAdjustFee.matches(reg3)) {
                            throw new Exception("营销费用调整费最多支持两位小数，请确认");
                        }
                    }

                    Long channelEntityId = Long.valueOf(nodeInfos.get(0).get("CHANNEL_ENTITY_ID").toString());
                    List<ChannelNodeDtl> channelNodeDtls = channelNodeDao.getChannelNodeInfo(channelEntityId);
                    if (channelNodeDtls.size() > 0) {
                        regionId = channelNodeDtls.get(0).getDistrictId();
                    }

                    BusiThirdXXAssess busiThirdXXAssess = new BusiThirdXXAssess();
                    busiThirdXXAssess.setDoneCode(doneCode);
//                    busiThirdXXAssess.setChannelTeam(channelTeam);
                    busiThirdXXAssess.setRoleName(roleName);
                    busiThirdXXAssess.setChannelEntityId(channelEntityId);
                    busiThirdXXAssess.setChannelEntityName(nodeName);
                    busiThirdXXAssess.setBillMonthScore(Double.parseDouble(billMonthScore));
                    busiThirdXXAssess.setAssesGridScore(Double.parseDouble(assesGridScore));
                    busiThirdXXAssess.setBasicCostFee(Double.parseDouble(basicCostFee));
                    busiThirdXXAssess.setMarketAdjustFee(Double.parseDouble(marketAdjustFee));
                    /*busiThirdXXAssess.setAssesmentScore(Integer.parseInt(assesmentScore));
                    busiThirdXXAssess.setAssesTTScore(Integer.parseInt(assesTTScore));
                    busiThirdXXAssess.setNegativePenaltyScore(Integer.parseInt(negativePenaltyScore));*/
                    busiThirdXXAssess.setOrgId(orgId);
                    busiThirdXXAssess.setOrgName(orgName);
                    busiThirdXXAssess.setOpId(opId);
                    busiThirdXXAssess.setBillMonth(billMonthMinus1);
                    busiThirdXXAssess.setRecStatus(0);
                    busiThirdXXAssess.setDoneDate(new Date());
                    busiThirdXXAssess.setAgentAdjustUseId(agentAdjustUseId);
                    busiThirdXXAssess.setFileName(remoteFileName);
                    busiThirdXXAssess.setFilePath(remotePath);

                    // 校验基础费和调整费
//                    this.checkBusiThirdFee(billMonthMinus1, channelEntityId, totalFee, basicTotalFee, busiThirdXXAssess);
                    totalFee += busiThirdXXAssess.getMarketAdjustFee()*100;
                    busiThirdXXAssessDao.insert(busiThirdXXAssess);
//                    agentServiceFeeService.addBusiThirdXXAssessInfoInfoForBatch(busiThirdXXAssess);

                    row.add("成功");
                    row.add("");
                }
            } catch (Exception e) {
                logger.error("属地第三方人员月度考核及相关费用-批量录入失败", e);
                while (row.size() < 1) {
                    row.add("");
                }
                row.add("失败");
                row.add(e.getMessage());
            } finally {
                returnList.add(row);
            }
        }

        {
//            ChannelSysBaseType channelSysBaseType = new ChannelSysBaseType();
//            channelSysBaseType.setCodeType(10002);
//            channelSysBaseType.setCodeId(regionId);
//            List<ChannelSysBaseType> channelSysBaseTypeList = channelSysBaseTypeDao.query(channelSysBaseType);
//            channelSysBaseType = channelSysBaseTypeList.get(0);
//            String regionCode = channelSysBaseType.getExt1();
            
            //若录入的调整费+营销费用超 属地初始营销费用总和，则系统提示：“营销费用调整费录入金额超上限，请修改”。
            if (totalFee > 0 ) {
                throw new Exception("营销费用调整费录入金额超上限，请修改！！");
            }

//            if (StringUtils.isNotNullOrBlank(regionCode)) {
//                RwdTpmsRegionAward rwdTpmsRegionAward = new RwdTpmsRegionAward();
//                rwdTpmsRegionAward.setRegionCode(Long.parseLong(regionCode));
//                rwdTpmsRegionAward.setAwardMonth(billMonthMinus1);
//                rwdTpmsRegionAward.setAwardType("1");
//                rwdTpmsRegionAward.setAwardState("1");
//                List<RwdTpmsRegionAward> rwdTpmsRegionAwardList = rwdTpmsRegionAwardDao.query(rwdTpmsRegionAward);
//
//                if (rwdTpmsRegionAwardList.size() > 0) {
//                    rwdTpmsRegionAward = rwdTpmsRegionAwardList.get(0);
//                    Double awardSaleFee = Double.parseDouble(rwdTpmsRegionAward.getAwardSaleFee()) / 100 * Double.parseDouble(rwdTpmsRegionAward.getSaleRatio()) / 100;
//                    Double awardTotalFee = Double.parseDouble(rwdTpmsRegionAward.getAwardTotalFee()) / 100;
//                    if (totalFee > awardSaleFee) {
//                        throw new Exception("属地各网点营销费用总和高于分公司初始营销费用，请确认！！");
//                    }
//                    if (basicTotalFee > awardTotalFee) {
//                        throw new Exception("属地各网点基本费用总和高于分公司基本费用初始核定总额，请确认！！");
//                    }
//                } else {
//                    throw new Exception("当月该属地没有初始营销费用金额，请确认！");
//                }
//            }
        }

        // 发送短信给下一级审批人
        try {
            // 查询审批人信息
            AgentAdjustUse adjustUse = new AgentAdjustUse();
            adjustUse.setAgentAdjustType(2);
            adjustUse.setAgentAdjustUseId(agentAdjustUseId);
            List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(adjustUse);

            String name = sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-属地第三方人员月度考核及相关费用录入审批-" +
                    DateUtil.formatDate(new Date(), "yyyyMMdd") + "-" + doneCode;
            String message = "提示：您的渠道管理系统有一张待审批工单，工单名称为" + name + "";

            sPrivData.setOpId(999990131L);
            sPrivData.setOrgId(0L);
            channelNotifyService.sendSMSMessage("100011051243", String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), message, DateUtil.getCurrDate(), sPrivData);

        } catch (Exception e) {
            logger.error("发送短信给审批人异常，失败原因是：", e);
        }

        return returnList;
    }

    public void checkBusiThirdFee(String billMonth, Long channelEntityId, Double totalFee, Double basicTotalFee,BusiThirdXXAssess busiThirdXXAssess) throws Exception{
        Double billMonthScore = busiThirdXXAssess.getBillMonthScore()/100;
        Double basicCostFee = busiThirdXXAssess.getBasicCostFee();          //基础费用分配
        Double marketAdjustFee = busiThirdXXAssess.getMarketAdjustFee();    //营销费用分配
        RwdTpmsRegionAward rwdTpmsRegionAward = new RwdTpmsRegionAward();
        rwdTpmsRegionAward.setRegionCode(channelEntityId);
        rwdTpmsRegionAward.setAwardType("2");
        rwdTpmsRegionAward.setAwardState("1");
        List<RwdTpmsRegionAward> rwdTpmsRegionAwardList = rwdTpmsRegionAwardDao.query(rwdTpmsRegionAward);
        if (rwdTpmsRegionAwardList.size() == 1){
            rwdTpmsRegionAward = rwdTpmsRegionAwardList.get(0);
            Double awardSaleFee = Double.parseDouble(rwdTpmsRegionAward.getAwardSaleFee())/100;
            totalFee += awardSaleFee * billMonthScore + marketAdjustFee;
            basicTotalFee += basicCostFee * billMonthScore;
        }else{
            totalFee += marketAdjustFee;
            basicTotalFee += basicCostFee * billMonthScore;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public void addBusiThirdXXAssessInfoInfoForBatch(BusiThirdXXAssess busiThirdXXAssess) throws Exception {
        //批量录入：覆盖信息（即批量导入信息直接覆盖系统内原信息）
        /*{
            BusiThirdXXAssess busiThirdXXAssessInfo = new BusiThirdXXAssess();
            busiThirdXXAssessInfo.setChannelEntityId(channelFuseItem.getChannelEntityId());
            busiThirdXXAssessInfo.setStatus(0);
            channelFuseItemDao.update(busiThirdXXAssessInfo);
        }*/
        try {
            busiThirdXXAssessDao.insert(busiThirdXXAssess);
        }catch (Exception e){
            logger.info("录入数据异常，原因是：" + e);
            throw new Exception("录入数据异常，原因是：" + e);
        }

    }

    @Override
    public PageData<BusiThirdXXAssess> busiThirdXXAssessInfoQuery(BusiThirdXXAssess busiThirdXXAssess, PageParameter pageParameter) {
        boolean isManageMenu = StringUtils.isNullOrBlank(busiThirdXXAssess.getRecStatusList());
        try{
            if (!isManageMenu){
                // 管理界面不需要审批人的条件
                Long agentAdjustUseId = agentAdjustUseDao.getIdByUserName(busiThirdXXAssess.getUserName());
                if (agentAdjustUseId != null) {
                    busiThirdXXAssess.setAgentAdjustUseId(agentAdjustUseId);
                } else {
                    logger.info("查不到该审批人！");
                    return new PageData<BusiThirdXXAssess>(null, pageParameter);
                }
            }
        }catch(Exception e){
            logger.error(""+e);

        }

        List<BusiThirdXXAssess> busiThirdXXAssessList = busiThirdXXAssessDao.query(busiThirdXXAssess, pageParameter);
        if (busiThirdXXAssessList.size() > 0) {
            for (BusiThirdXXAssess entity : busiThirdXXAssessList) {
                if (entity.getRecStatus() == 0) {
                    entity.setRecStatusApprove("待属地市场部三级审批");
                    this.setAdjustUseName(entity);
                } else if (entity.getRecStatus() == 1) {
                    entity.setRecStatusApprove("待属地市场部二级审批");
                    this.setAdjustUseName(entity);
                } else if (entity.getRecStatus() == 2) {
                    entity.setRecStatusApprove("属地市场部三级审批拒绝");
                } else if (entity.getRecStatus() == 3) {
                    entity.setRecStatusApprove("审批通过");
                } else if (entity.getRecStatus() == 4) {
                    entity.setRecStatusApprove("属地市场部二级审批拒绝");
                }
            }
        }
        if (isManageMenu){
            return new PageData<BusiThirdXXAssess>(busiThirdXXAssessList, pageParameter);
        }
        // 不知为何使用pageParameter.getTotalCount() 前台就能展示数据了
        return new PageData<BusiThirdXXAssess>(busiThirdXXAssessList, pageParameter.getTotalCount());

    }

    @Override
    public PageData<BusinessThirdAssessment> businessThirdAssessmentQuery(BusinessThirdAssessment businessThirdAssessment, PageParameter pageParameter) {
        List<BusinessThirdAssessment> businessThirdAssessmentList =null;
        if (businessThirdAssessment.getRecStatus() !=null){
            String auditStatusList = "0,1";
            businessThirdAssessment.setAuditStatusList(auditStatusList);
            String userName = businessThirdAssessment.getUserName();
            Long agentAdjustUseId = agentAdjustUseDao.getIdByUserName(userName);
            if (agentAdjustUseId != null){
                businessThirdAssessment.setAgentAdjustUseId(agentAdjustUseId);
            }else{
                logger.info("没有该审批人审批信息！！！");
                return new PageData<BusinessThirdAssessment>(businessThirdAssessmentList, pageParameter);
            }
        }

        businessThirdAssessmentList = businessThirdAssessmentDao.queryBusinessThirdAssessmentInfo(businessThirdAssessment, pageParameter);
        if (businessThirdAssessmentList.size()>0){
            for (BusinessThirdAssessment entity:businessThirdAssessmentList) {
                if (entity.getAuditStatus() == 0){
                    entity.setRecStatusApprove("待属地三级审批");
                }else if (entity.getAuditStatus() == 1){
                    entity.setRecStatusApprove("待属地二级审批");
                }else if (entity.getAuditStatus() == 2){
                    entity.setRecStatusApprove("属地三级审批拒绝");
                }else if (entity.getAuditStatus() == 3){
                    entity.setRecStatusApprove("审批通过");
                }else if (entity.getAuditStatus() == 4){
                    entity.setRecStatusApprove("属地二级审批拒绝");
                }
            }
        }
        if (businessThirdAssessment.getRecStatus() !=null){
            return new PageData<BusinessThirdAssessment>(businessThirdAssessmentList, pageParameter.getTotalCount());
        }
        return new PageData<BusinessThirdAssessment>(businessThirdAssessmentList, pageParameter);

    }

    @Override
    public void busiThirdXXAssessEdit(BusiThirdXXAssess busiThirdXXAssess,SPrivData sPrivData) throws Exception {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.MONTH, -1);
        String billMonthMinus1 = new SimpleDateFormat("yyyyMM").format(cal.getTime());

        try {
            if (busiThirdXXAssess.getRecStatus() == null ||
                    busiThirdXXAssess.getRecStatus() == -1) { // 管理界面修改数据
                logger.debug("busiThirdXXAssessEdit getRecStatus() == -1 || null");
                // 保留原数据
                busiThirdXXAssess.setRecStatus(-1);
                busiThirdXXAssessDao.update(busiThirdXXAssess);

                // 修改的数据其实是新增的
                busiThirdXXAssess.setDoneDate(new Date());
                busiThirdXXAssess.setRecStatus(0);
                busiThirdXXAssessDao.insert(busiThirdXXAssess);
                sendSms1: try {
                    // 查询审批人信息
                    AgentAdjustUse queryAdjustUse = new AgentAdjustUse();
                    queryAdjustUse.setAgentAdjustType(2);
                    queryAdjustUse.setAgentAdjustUseId(busiThirdXXAssess.getAgentAdjustUseId());
                    List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(queryAdjustUse);

                    // 发送短信
                    String name = sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-第三方行销人员考核审批-" +
                            DateUtil.formatDate(new Date(), "yyyyMMdd") + "-" + busiThirdXXAssess.getDoneCode();
                    String message = "提示：您的渠道管理系统有一张待审批工单，工单名称为" + name + "";
                    logger.info(message);
                    sPrivData.setOpId(999990131L);
                    sPrivData.setOrgId(0L);
                    String phoneId = String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone());
                    channelNotifyService.sendSMSMessage("100011051243", phoneId, message, DateUtil.getCurrDate(), sPrivData);

                } catch (Exception e) {
                    logger.error("发送短信给审批人异常，失败原因是：", e);
                }

            } else { // 审批通过（1,3）或者审批驳回（2,4）
                // 发送给二级经理
                sendSms2: if (busiThirdXXAssess.getRecStatus() == 1) {
                    try {
                        // 查询审批人信息
                        AgentAdjustUse queryAdjustUse = new AgentAdjustUse();
                        queryAdjustUse.setAgentAdjustType(3); // 第一层审批的人type=2，第二层审批的人type=3，也就是三级经理对应type=2，二级经理type=3
                        queryAdjustUse.setAgentAdjustUseId(busiThirdXXAssess.getAgentAdjustUseId());
                        List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(queryAdjustUse);

                        // 发送短信
                        String name = sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-第三方行销人员考核审批-" +
                                DateUtil.formatDate(new Date(), "yyyyMMdd") + "-" + busiThirdXXAssess.getDoneCode();
                        String message = "提示：您的渠道管理系统有一张待审批工单，工单名称为" + name + "";
                        logger.info(message);
                        sPrivData.setOpId(999990131L);
                        sPrivData.setOrgId(0L);
                        String phoneId = String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone());
                        channelNotifyService.sendSMSMessage("100011051243", phoneId, message, DateUtil.getCurrDate(), sPrivData);

                    } catch (Exception e) {
                        logger.error("发送短信给审批人异常，失败原因是：", e);
                    }
                }

                // 发送短信给一开始的录入人
                sendSms3: if (busiThirdXXAssess.getRecStatus() == 3) {
                    // 查询录入人信息
                    Long orgId = busiThirdXXAssess.getOrgId();
                    BusiThirdDependRole busiThirdDependRole = new BusiThirdDependRole();
                    busiThirdDependRole.setRecStatus(1);
                    busiThirdDependRole.setOrgId(orgId);
                    List<BusiThirdDependRole> busiThirdDependRoleList = busiThirdDependRoleDao.query(busiThirdDependRole);
                    if (busiThirdDependRoleList.size() == 0) {
                        break sendSms3;
                    }
                    // 发送完成短信
                    // 提示：您的渠道管理系统工单：【所属组织】-【员工姓名】-【项目及日期】-【序列号】，已结束流程
                    String message = "提示：您的渠道管理系统工单：" + sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-" + "第三方行销人员考核审批" + DateUtil.formatDate(new Date(),
                            "yyyyMMdd") + "-" + busiThirdXXAssess.getDoneCode() + "，已结束流程";
                    logger.info(message);
                    sPrivData.setOpId(999990131L);
                    sPrivData.setOrgId(0L);
                    String phoneId = String.valueOf(busiThirdDependRoleList.get(0).getPhoneNo());
                    channelNotifyService.sendSMSMessage("100011061587", phoneId, message, DateUtil.getCurrDate(), sPrivData);
                }

                busiThirdXXAssess.setDoneDate(new Date());
                busiThirdXXAssess.setBillMonth(billMonthMinus1);
                busiThirdXXAssessDao.update(busiThirdXXAssess);
            }

        } catch (Exception e) {
            throw new Exception("修改数据失败，失败原因" + ExceptionUtil.buildMessage(e));
        }
    }


    //属地第三方直销费用调整录入
    @Override
    public List<List<Object>> threeNodeNameFeeBatchAdd(List<List<Object>> list, Long agentAdjustUseId, String remoteFileName, String remotePath, SPrivData sPrivData) throws Exception {
        List<List<Object>> returnList = new ArrayList<List<Object>>();

        Long orgId = sPrivData.getOrgId();
        String orgName = sPrivData.getOrgName();
        Long opId = sPrivData.getOpId();

        Date date = new Date();
        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMM");
        Calendar cal=Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH,-1);
        String billMonth=sdf.format(cal.getTime());
        
        //市场-2025-5712_渠道二期新增集团成员个性化资费录入菜单
        // 清理过期数据
        ThreeNodeNameFee threeNodeNameFeeTemp = new ThreeNodeNameFee();
        threeNodeNameFeeTemp.setOrgId(orgId);
        threeNodeNameFeeTemp.setTheMonth(billMonth);
        threeNodeNameFeeTemp.setRecStatus(-1);
        threeNodeNameFeeDao.update(threeNodeNameFeeTemp);

        //获取序列号
        Long doneCode = threeNodeNameFeeDao.getSequence();
        //[0,10] [0,90]整数 正则校验

//        String reg1 = "^-\\d+(\\.\\d{1,2})?$";
//        String reg1 = "^[0-9]+(.[0-9]{1,2})?$";
        String reg1 = "^(\\-)?\\d+(\\.\\d{1,2})$";
        String reg2 = "^0(\\.0{0,2})?$|^-(0\\.\\d*[1-9]\\d*|[1-9]\\d*(\\.\\d{1,2})?)$";
//        String reg2 = "^\\-\\d+(\\.\\d{1,2})$";
//        String reg1 = "/^-?(0|([1-9][0-9]*))(\\.[\\d]{1,2})$/";
//        String reg2 = "^(0|([1-8]?[0-9]|90))$";
        for (int i = 0; i < list.size(); i++) {
            List<Object> row = list.get(i);
            try {
                if (i == 0) {
                    row.add("结果");
                    row.add("备注");
                } else {
                    if (row.size() != 4) {
                        throw new Exception("列数不满足");
                    }
                    String nodeName = row.get(0) == null ? "" : row.get(0).toString();
                    if (StringUtils.isNullOrBlank(nodeName)){
                        throw new Exception("网点名称不能为空");
                    }
                    List<Map<String, Object>> nodeInfos = channelFuseItemDao.queryNodeByName(nodeName);
                    if (nodeInfos.size() < 1) {
                        throw new Exception("请确认该网点名称是否存在、状态是否正常");
                    }
                    if (nodeInfos.size() > 1) {
                        throw new Exception("该网点名称在系统中存在多条，请确认");
                    }

                    String adjustFee = row.get(1) == null ? "" : row.get(1).toString().trim();
                    //因为工具类原因会把输入的得分转Double 然后转String
                    String qualityChase = row.get(2) == null ? "" : row.get(2).toString().trim();
                    String outDeduction = row.get(3) == null ? "" : row.get(3).toString().trim();

//                    String negativePenaltyScore = row.get(4) == null ? "" : row.get(4).toString().trim();
                    if (StringUtils.isNullOrBlank(adjustFee)){
                        throw new Exception("调整费不能为空");
                    }
                    if (StringUtils.isNullOrBlank(qualityChase)){
                        throw new Exception("质量追溯不能为空");
                    }
                    if (StringUtils.isNullOrBlank(outDeduction)){
                        throw new Exception("违规抵扣不能为空");
                    }
                    if (!adjustFee.matches(reg1)){
                        throw new Exception("调整费输入不符合规范，请重新输入");
                    }
                    if (!qualityChase.matches(reg2)){
                        throw new Exception("质量追溯输入不符合规范，请重新输入");
                    }
                    if (!outDeduction.matches(reg2)){
                        throw new Exception("违规抵扣输入不符合规范，请重新输入");
                    }

                    Long channelEntityId = Long.valueOf(nodeInfos.get(0).get("CHANNEL_ENTITY_ID").toString());

                    ThreeNodeNameFee threeNodeNameFee = new ThreeNodeNameFee();
                    threeNodeNameFee.setAgentAdjustUseId(agentAdjustUseId);
                    threeNodeNameFee.setTheMonth(billMonth);
                    threeNodeNameFee.setOrgName(orgName);
                    threeNodeNameFee.setOrgId(orgId);
                    threeNodeNameFee.setChannelEntityName(nodeName);
                    threeNodeNameFee.setChannelEntityId(channelEntityId);
                    threeNodeNameFee.setAdjustFee(Double.parseDouble(adjustFee));
                    threeNodeNameFee.setQualityChase(Double.parseDouble(qualityChase));
                    threeNodeNameFee.setOutDeduction(Double.parseDouble(outDeduction));
                    threeNodeNameFee.setOpId(opId);
                    threeNodeNameFee.setRecStatus(0);
                    threeNodeNameFee.setDoneDate(new Date());
                    threeNodeNameFee.setFaceId(doneCode);
                    threeNodeNameFee.setFileName(remoteFileName);
                    threeNodeNameFee.setFilePath(remotePath);
                    List<Map<String, Object>> nodeNameFees = threeNodeNameFeeDao.querySameInfo(threeNodeNameFee);
                    if (nodeNameFees.size() > 0) {
                        throw new Exception("该网点名称工单已经处于待审批或审批通过状态，不允许二次录入，请确认");
                    }

                    threeNodeNameFeeDao.insert(threeNodeNameFee);
                    row.add("成功");
                    row.add("");
                }
            } catch (Exception e) {
                logger.error("", e);
                while (row.size() < 1) {
                    row.add("");
                }
                row.add("失败");
                row.add(e.getMessage());
            } finally {
                returnList.add(row);
            }
        }

        try{
            //查询审批人信息
            AgentAdjustUse adjustUse = new AgentAdjustUse();
            adjustUse.setAgentAdjustType(2);
            adjustUse.setAgentAdjustUseId(agentAdjustUseId); // 将id临时保存的，暂存
            List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(adjustUse);
            //开始定义短信信息，并调接口发送至审批人手机
            String name =sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-第三方行销人员费用审批-" +
                    DateUtil.formatDate(new Date(),"yyyyMMdd") + "-" + doneCode;
            String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为"+name+"";
            logger.info(messager1);
            String smsCode = "100011051243";
            SPrivData sPrivData1 = sPrivData;
            sPrivData1.setOpId(999990131L);
            sPrivData1.setOrgId(0L);
            channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);

        }catch (Exception e){
            logger.error("发送短信给审批人异常，失败原因是：" + e);
        }
        return returnList;
    }


    @Override
    public PageData<ThreeNodeNameFee> threeNodeNameFeeInfoQuery(ThreeNodeNameFee threeNodeNameFee, PageParameter pageParameter) {
        List<ThreeNodeNameFee> threeNodeNameFeeList =null;
        try {
            if (threeNodeNameFee.getRecStatus() != null){
                String userName = threeNodeNameFee.getUserName();
                Long agentAdjustUseId = agentAdjustUseDao.getIdByUserName(userName);
                if (agentAdjustUseId != null){
                    threeNodeNameFee.setAgentAdjustUseId(agentAdjustUseId);
                }else{
                    logger.info("没有该审批人审批信息！！！");
                    return new PageData<ThreeNodeNameFee>(threeNodeNameFeeList, pageParameter);
                }
                threeNodeNameFeeList = threeNodeNameFeeDao.queryThreeNodeNameFeeInfo(threeNodeNameFee, pageParameter);
                return new PageData<ThreeNodeNameFee>(threeNodeNameFeeList, pageParameter.getTotalCount());
            }else{
                threeNodeNameFeeList = threeNodeNameFeeDao.queryThreeNodeNameFeeInfo1(threeNodeNameFee, pageParameter);
                if (threeNodeNameFeeList.size()>0){
                    for (ThreeNodeNameFee entity:threeNodeNameFeeList) {
                        if (entity.getRecStatus() == 0){
                            entity.setRecStatusApprove("待审批");
                        }else if (entity.getRecStatus() == 1){
                            entity.setRecStatusApprove("审批通过");
                        }else if (entity.getRecStatus() == 2){
                            entity.setRecStatusApprove("审批驳回");
                        }
                    }
                }
                return new PageData<ThreeNodeNameFee>(threeNodeNameFeeList, pageParameter);
            }
        }catch (Exception e){
            logger.error("");
        }
        return new PageData<ThreeNodeNameFee>(threeNodeNameFeeList, pageParameter);
    }


    @Override
    public void threeNodeNameFeeEdit(ThreeNodeNameFee threeNodeNameFee) throws Exception {
        //因审批和修改走同一套逻辑 故判断其控制层如果没有传 状态 则状态给-1：失效  传了状态直接给 2：审批通过 或 3：审批驳回
        //对传了状态的数据 直接进行修改，而数据修改的 需要把原先数据置失效，再添加数据
        Date date = new Date();
        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMM");
        Calendar cal=Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH,-1);
        String billMonth=sdf.format(cal.getTime());
        ThreeNodeNameFee threeNodeNameFeetemp = threeNodeNameFee;
        if (threeNodeNameFeetemp.getRecStatus() == null) {
            threeNodeNameFeetemp.setRecStatus(-1);
        }
        try{
            if(threeNodeNameFeetemp.getRecStatus() == -1){
                threeNodeNameFeeDao.update(threeNodeNameFeetemp);
                threeNodeNameFee.setDoneDate(new Date());
                threeNodeNameFee.setRecStatus(0);
                threeNodeNameFeeDao.insert(threeNodeNameFee);
            } else {
                threeNodeNameFeetemp.setDoneDate(new Date());
                threeNodeNameFeetemp.setTheMonth(billMonth);
                threeNodeNameFeeDao.update(threeNodeNameFeetemp);
            }
        }catch (Exception e){
            logger.error("修改数据失败，失败原因是："+e);
            throw new Exception("修改数据失败，失败原因" +e);
        }
    }

    @Override
    public void businessThirdAssessmentEdit(BusinessThirdAssessment businessThirdAssessment,SPrivData sPrivData) throws Exception {
        //因审批和修改走同一套逻辑 故判断其控制层如果没有传 状态  则查询界面的修改状态给0：失效  传了状态直接给 1：审批通过 或 2：审批驳回
        //对传了状态的数据 直接进行修改，而查询界面数据修改的 需要把原先数据置失效，再添加数据
        Date date = new Date();
        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMM");
        Calendar cal=Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH,-1);
        String billMonth=sdf.format(cal.getTime());
        BusinessThirdAssessment businessThirdAssessmentTemp = businessThirdAssessment;
        //获取序列号
        //        Long doneCode = businessThirdAssessmentDao.getSequence();
        Long doneCode = 1L;
        if (businessThirdAssessmentTemp.getAuditStatus() == null) {
            businessThirdAssessmentTemp.setRecStatus(0);
        }
        try{
            if(businessThirdAssessmentTemp.getAuditStatus() == 1){//查询界面修改 走审批流程
                businessThirdAssessmentTemp.setDoneDate(new Date());
                businessThirdAssessmentDao.update(businessThirdAssessmentTemp);
//                businessThirdAssessmentTemp.setDoneCode(doneCode);
//                businessThirdAssessmentTemp.setDoneDate(new Date());
//                businessThirdAssessmentTemp.setRecStatus(1);
//                businessThirdAssessmentTemp.setAuditStatus(0);
//                businessThirdAssessmentDao.insert(businessThirdAssessmentTemp);
                try{
                    //查询审批人信息
                    AgentAdjustUse adjustUse = new AgentAdjustUse();
                    adjustUse.setAgentAdjustType(2);
                    adjustUse.setAgentAdjustUseId(businessThirdAssessmentTemp.getAgentAdjustUseId()); // 将id临时保存的，暂存
                    List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(adjustUse);
                    //开始定义短信信息，并调接口发送至审批人手机
                    String name =sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-第三方行销人员考核审批-" +
                            DateUtil.formatDate(new Date(),"yyyyMMdd") + "-" + doneCode;
                    String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为" + name + "";
                    logger.info(messager1);
                    String smsCode = "100011051243";
                    SPrivData sPrivData1 = sPrivData;
                    sPrivData1.setOpId(999990131L);
                    sPrivData1.setOrgId(0L);
                    channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);

                }catch (Exception e){
                    logger.error("发送短信给审批人异常，失败原因是：" + e);
                }
            } else {
                businessThirdAssessmentTemp.setDoneDate(new Date());
                businessThirdAssessmentTemp.setBillMonth(Integer.parseInt(billMonth));
                businessThirdAssessmentDao.update(businessThirdAssessmentTemp);
            }

        }catch (Exception e){
            logger.error("修改数据失败，失败原因是："+e);
            throw new Exception("修改数据失败，失败原因" +e);
        }
    }
    @Override
    public void businessThirdAssessmentAdd(BusinessThirdAssessment businessThirdAssessment, SPrivData sPrivData) throws Exception {

        //市场-2025-5712_渠道二期新增集团成员个性化资费录入菜单 清理过期数据
        BusinessThirdAssessment businessThirdAssessmentDel = new BusinessThirdAssessment();
        businessThirdAssessmentDel.setBillMonth(businessThirdAssessment.getBillMonth());
        businessThirdAssessmentDel.setOrgId(businessThirdAssessment.getOrgId());
        businessThirdAssessmentDel.setRecStatus(-1);
        businessThirdAssessmentDel.setAuditStatus(-1);
        businessThirdAssessmentDao.update(businessThirdAssessmentDel);
        
        //获取序列号
        Long doneCode = businessThirdAssessmentDao.getSequence();
        businessThirdAssessment.setDoneCode(doneCode);
        businessThirdAssessment.setRecStatus(1);
        businessThirdAssessment.setDoneDate(new Date());
        businessThirdAssessmentDao.insert(businessThirdAssessment);

        try{
            //查询审批人信息
            AgentAdjustUse adjustUse = new AgentAdjustUse();
            adjustUse.setAgentAdjustType(2);
            adjustUse.setAgentAdjustUseId(businessThirdAssessment.getAgentAdjustUseId()); // 将id临时保存的，暂存
            List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(adjustUse);
            //开始定义短信信息，并调接口发送至审批人手机
            String name =sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-第三方行销费用审批-" +
                    DateUtil.formatDate(new Date(),"yyyyMMdd") + "-" + businessThirdAssessment.getDoneCode();
            //提示：您的渠道管理系统有一张待审批工单，市场部-孟越超-属地第三方直销支撑项目月度考核得分录入-20250402-01
            String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为 "+name+".";
            logger.info(messager1);
            String smsCode = "100011051243";
            SPrivData sPrivData1 = sPrivData;
            sPrivData1.setOpId(999990131L);
            sPrivData1.setOrgId(0L);
            channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);

        }catch (Exception e){
            logger.error("发送短信给审批人异常，失败原因是：" + e);
        }

    }

    @Override
    public void checkCompanySettleAdd(CompanySpecSettlement companySpecSettlement) throws Exception {
        //判断记录是否存在
        List<CompanySpecSettlement> companySpecSettlementList = companySpecSettlementDao.query(companySpecSettlement);
        if (companySpecSettlementList.size() > 0) {
            throw new Exception("本月数据已录入，请至在线公司专席结算录入页面进行修改!");
        }
    }

    @Override
    public PageData<CompanySpecSettlement> companySpecSettlementQuery(CompanySpecSettlement companySpecSettlement, PageParameter pageParameter) {
        List<CompanySpecSettlement> companySpecSettlementList = null;
        try{
            companySpecSettlementList = companySpecSettlementDao.queryCompanySpecSettlement(companySpecSettlement,pageParameter);
        }catch (Exception e){
            logger.info("查询数据失败，失败原因是：" +e);
        }
        return new PageData<CompanySpecSettlement>(companySpecSettlementList, pageParameter);
    }

    @Override
    public void companySpecSettlementEdit(CompanySpecSettlement companySpecSettlement) throws Exception {
        CompanySpecSettlement companySpecSettlement1 = companySpecSettlement;
        try{
            companySpecSettlement1.setDoneDate(new Date());
            companySpecSettlement1.setRecStatus(0);
            companySpecSettlementDao.update(companySpecSettlement);
            companySpecSettlement1.setRecStatus(1);
            companySpecSettlementDao.insert(companySpecSettlement1);
        }catch (Exception e){
            logger.error("修改数据失败，失败原因是："+e);
            throw new Exception("修改数据失败，失败原因" +e);
        }
    }

    @Override
    public List<List<Object>> busiFuseProInfo(List<List<Object>> list, SPrivData sPrivData) throws Exception {
        List<List<Object>> returnList = new ArrayList<List<Object>>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyydd");
        Long orgId =sPrivData.getOrgId();
        String orgName= sPrivData.getOrgName();
        Long opId = sPrivData.getOpId();
        for (int i = 0; i < list.size(); i++) {
            List<Object> row = list.get(i);
            try {
                if (i == 0) {
                    row.add("结果");
                    row.add("备注");
                } else {
                    if (row.size() != 1) {
                        throw new Exception("列数不满足");
                    }

                    Date date = new Date();
                    SimpleDateFormat sdf2 =new SimpleDateFormat("yyyyMM");
                    Calendar cal=Calendar.getInstance();
                    cal.setTime(date);
                    cal.add(Calendar.MONTH,-1);
                    String billMonth=sdf2.format(cal.getTime());
                    BusiFusePro busiFusePro = new BusiFusePro();
                    String channelEntityName = row.get(0) == null ? "" : row.get(0).toString();
                    if (StringUtils.isNullOrBlank(channelEntityName)){
                        throw new Exception("网点名称不能为空");
                    }
                    List<Map<String, Object>> nodeInfos = busiFuseProDao.queryNodeByName(channelEntityName);
                    if (nodeInfos.size() < 1) {
                        throw new Exception("请确认该网点名称是否存在、状态是否正常");
                    }
                    if (nodeInfos.size() > 1) {
                        throw new Exception("该网点名称在系统中存在多条，请确认");
                    }
                    //判断网点
                    List<Map<String, Object>> nodeInfos2 = busiFuseProDao.queryNodeAndMonth(channelEntityName,billMonth);
                    if (nodeInfos2.size() > 0) {
                        throw new Exception("该网点本月已经录入！");
                    }
                    busiFusePro.setChannelEntityId(Long.valueOf(nodeInfos.get(0).get("CHANNEL_ENTITY_ID").toString()));
                    busiFusePro.setBillMonth(billMonth);
                    busiFusePro.setOpId(opId);
                    busiFusePro.setOrgId(orgId);
                    busiFusePro.setOrgName(orgName);
                    busiFusePro.setBillMonth(billMonth);
                    busiFusePro.setChannelEntityName(channelEntityName);
                    busiFusePro.setDoneDate(new Date());
                    busiFusePro.setRecStatus(1);
                    busiFuseProDao.insert(busiFusePro);
                    row.add("成功");
                    row.add("");
                }
            } catch (Exception e) {
                logger.error("", e);
                while (row.size() < 1) {
                    row.add("");
                }
                row.add("失败");
                row.add(e.getMessage());
            } finally {
                returnList.add(row);
            }
        }

        return returnList;
    }

    @Override
    public PageData<BusiFusePro> queryBusiFuseProInfo(BusiFusePro busiFusePro, PageParameter pageParameter) {
        List<BusiFusePro> busiFuseProList =null;
        try {
            busiFuseProList = busiFuseProDao.queryBusiFuseProInfo(busiFusePro, pageParameter);
        }catch (Exception e){
            logger.error("查询数据失败，失败原因是" + e);
        }
        return new PageData<BusiFusePro>(busiFuseProList, pageParameter);
    }

    @Override
    public void delBusiFuseProInfo(BusiFusePro busiFusePro) throws Exception {
        busiFusePro.setRecStatus(0);
        busiFusePro.setDoneDate(new Date());
        busiFuseProDao.update(busiFusePro);
    }



    @Override
    public List<List<Object>> busiThirdSalesRoleUpdInfo(List<List<Object>> list, Long agentAdjustUseId, String remoteFileName, String remotePath, SPrivData sPrivData) throws Exception {
        List<List<Object>> returnList = new ArrayList<List<Object>>();
        Date date = new Date();
        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMM");
        Calendar cal=Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH,-1);
        String billMonth=sdf.format(cal.getTime());
        Long orgId = sPrivData.getOrgId();
        String orgName = sPrivData.getOrgName();
        Long opId = sPrivData.getOpId();
        String userName = sPrivData.getOpName();
        //获取序列号
        Long doneCode = null;

        try{
            doneCode = busiThirdSalesRoleUpdDao.getSequence();
        }catch (Exception e){
            logger.error("获取第三方直销角色调整序列异常，异常原因是：" + e.getMessage());
            throw new Exception("获取第三方直销角色调整序列异常，异常原因是：" + e.getMessage());
        }
        //查询审批人信息
        /*AgentAdjustUse adjustUse = new AgentAdjustUse();
        adjustUse.setAgentAdjustType(2);
        adjustUse.setAgentAdjustUseId(agentAdjustUseId); // 将id临时保存的，暂存
        List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(adjustUse);
        if (agentAdjustUses.size() != 1){
            throw new Exception("第三方直销角色调整录入，所选择审批人不存在，请重新选择录入！");
        }*/
        for (int i = 0; i < list.size(); i++) {
            List<Object> row = list.get(i);
            try {
                if (i == 0) {
                    row.add("结果");
                    row.add("备注");
                } else {
                    if (row.size() != 2) {
                        throw new Exception("列数不满足");
                    }

                    String nodeName = row.get(0) == null ? "" : row.get(0).toString().trim();
                    String roleName = row.get(1) == null ? "" : row.get(1).toString().trim();
                    if (StringUtils.isNullOrBlank(roleName)){
                        throw new Exception("角色字段不能为空，请重新输入！！！");
                    }
                    if (ChannelSysBaseTypeUtil.getCodeId(10055, roleName) == null){
                        throw new Exception("角色字段不规范，请重新输入！！！");
                    }
                    if (StringUtils.isNullOrBlank(nodeName)) {
                        throw new Exception("网点工号名称不能为空,请重新输入！！！");
                    }

                    List<Map<String, Object>> nodeInfos = channelFuseItemDao.queryNodeByName(nodeName);
                    if (nodeInfos.size() < 1) {
                        throw new Exception("请确认该网点工号名称是否存在、状态是否正常");
                    }
                    if (nodeInfos.size() > 1) {
                        throw new Exception("该网点工号名称在系统中存在多条，请确认");
                    }
                    Long channelEntityId = Long.valueOf(nodeInfos.get(0).get("CHANNEL_ENTITY_ID").toString());
                    BusiThirdSalesRoleUpd busiThirdSalesRoleUpd = new BusiThirdSalesRoleUpd();
                    busiThirdSalesRoleUpd.setBillMonth(billMonth);
                    busiThirdSalesRoleUpd.setNodeName(nodeName);
                    busiThirdSalesRoleUpd.setNodeId(channelEntityId);
                    busiThirdSalesRoleUpd.setOrgId(orgId);
                    /*List<BusiThirdSalesRoleUpd> busiThirdSalesRoleUpdList = busiThirdSalesRoleUpdDao.query(busiThirdSalesRoleUpd);
                    if (busiThirdSalesRoleUpdList.size() > 0){
                        throw new Exception("该网点工号名称工单已经处于待审批或审批通过状态，不允许二次录入，请确认");
                    }*/

                    BusiThirdSalesRoleUpd busiThirdSalesRoleUpds = new BusiThirdSalesRoleUpd();
                    busiThirdSalesRoleUpds.setDoneCode(doneCode);
                    busiThirdSalesRoleUpds.setRoleName(roleName);
                    busiThirdSalesRoleUpds.setNodeId(channelEntityId);
                    busiThirdSalesRoleUpds.setNodeName(nodeName);
                    busiThirdSalesRoleUpds.setRoleId(ChannelSysBaseTypeUtil.getCodeId(10055, roleName));
                    busiThirdSalesRoleUpds.setUserName(userName);
                    busiThirdSalesRoleUpds.setOrgId(orgId);
                    busiThirdSalesRoleUpds.setOrgName(orgName);
                    busiThirdSalesRoleUpds.setOpId(opId);
                    busiThirdSalesRoleUpds.setBillMonth(billMonth);
                    busiThirdSalesRoleUpds.setRecStatus(1);
                    busiThirdSalesRoleUpds.setAuditStatus(3);
                    busiThirdSalesRoleUpds.setApproveStatus("审批通过");
//                    busiThirdSalesRoleUpds.setAgentAdjustUserName(agentAdjustUses.get(0).getAgentAdjustName());
                    busiThirdSalesRoleUpds.setDoneDate(new Date());
//                    busiThirdSalesRoleUpds.setAgentAdjustUseId(agentAdjustUseId);
                    busiThirdSalesRoleUpds.setFileName(remoteFileName);
                    busiThirdSalesRoleUpds.setFilePath(remotePath);
                    agentServiceFeeService.busiThirdSalesRoleUpdInfoInfoForBatch(busiThirdSalesRoleUpds);
                    //审批通过 修改网点的角色 获取审批信息中的调整信息更新进网点信息中
                    ChannelNode channelNode = new ChannelNode();
                    channelNode.setNodeId(channelEntityId);
                    channelNode.setIsThirdBusi(1);
                    channelNode.setBusiRole(ChannelSysBaseTypeUtil.getCodeId(10055, roleName));
                    channelNode.setDoneDate(new Date());
                    channelNodeDao.updateBusiRole(channelNode);
                    row.add("成功");
                    row.add("");
                }
            } catch (Exception e) {
                logger.error("", e);
                while (row.size() < 1) {
                    row.add("");
                }
                row.add("失败");
                row.add(e.getMessage());
            } finally {
                returnList.add(row);
            }
        }

        /*try{
            //开始定义短信信息，并调接口发送至审批人手机
            String name =sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-第三方直销角色调整申请属地审批-" +
                    DateUtil.formatDate(new Date(),"yyyyMMdd") + "-" + doneCode;
            String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为"+name+"";
            logger.info(messager1);
            String smsCode = "100011051243";
            SPrivData sPrivData1 = sPrivData;
            sPrivData1.setOpId(999990131L);
            sPrivData1.setOrgId(0L);
            channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);

        }catch (Exception e){
            logger.error("发送短信给审批人异常，失败原因是：" + e);
        }*/
        return returnList;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public void busiThirdSalesRoleUpdInfoInfoForBatch(BusiThirdSalesRoleUpd busiThirdSalesRoleUpd) throws Exception {
        //批量录入：覆盖信息（即批量导入信息直接覆盖系统内原信息）
        try {
            busiThirdSalesRoleUpdDao.insert(busiThirdSalesRoleUpd);
        }catch (Exception e){
            logger.info("录入数据异常，原因是：" + e);
            throw new Exception("录入数据异常，原因是：" + e);
        }

    }

    @Override
    public PageData<BusiThirdSalesRoleUpd> busiThirdSalesRoleUpdInfoQuery(BusiThirdSalesRoleUpd busiThirdSalesRoleUpd, PageParameter pageParameter) {
        List<BusiThirdSalesRoleUpd> busiThirdSalesRoleUpdList =null;
        try{
            if (busiThirdSalesRoleUpd.getRecStatus() !=null){
                /*String userName = busiThirdSalesRoleUpd.getUserName();
                Long agentAdjustUseId = agentAdjustUseDao.getIdByUserName(userName);
                if (agentAdjustUseId != null){
                    busiThirdSalesRoleUpd.setAgentAdjustUseId(agentAdjustUseId);
                }else{
                    logger.info("没有该审批人审批信息！！！");
                    return new PageData<BusiThirdSalesRoleUpd>(busiThirdSalesRoleUpdList, pageParameter);
                }*/
                busiThirdSalesRoleUpdList = busiThirdSalesRoleUpdDao.queryBusiThirdSalesRoleUpdInfo1(busiThirdSalesRoleUpd, pageParameter);
                return new PageData<BusiThirdSalesRoleUpd>(busiThirdSalesRoleUpdList, pageParameter.getTotalCount());
            }else{
                busiThirdSalesRoleUpdList = busiThirdSalesRoleUpdDao.queryBusiThirdSalesRoleUpdInfo(busiThirdSalesRoleUpd, pageParameter);
                if (busiThirdSalesRoleUpdList.size()>0){
                    for (int i = 0; i < busiThirdSalesRoleUpdList.size(); i++) {
                        if (busiThirdSalesRoleUpdList.get(i).getAuditStatus() == 2 || busiThirdSalesRoleUpdList.get(i).getAuditStatus() == 4){
                            busiThirdSalesRoleUpdList.get(i).setAgentAdjustUserName(null);
                            busiThirdSalesRoleUpdList.get(i).setAgentAdjustUseId(null);
                        }
                    }
                }
                return new PageData<BusiThirdSalesRoleUpd>(busiThirdSalesRoleUpdList, pageParameter);
            }
        }catch (Exception e){
            logger.error("" ,e);
            return new PageData<BusiThirdSalesRoleUpd>(busiThirdSalesRoleUpdList, pageParameter);
        }

    }

    @Override
    public void busiThirdSalesRoleUpdEdit(BusiThirdSalesRoleUpd busiThirdSalesRoleUpd,SPrivData sPrivData) throws Exception {
        //因审批和修改走同一套逻辑 故判断其控制层如果没有传 状态 则状态给-1：失效  传了状态直接给 2：审批通过 或 3：审批驳回
        //对传了状态的数据 直接进行修改，而数据修改的 需要把原先数据置失效，再添加数据
        Date date = new Date();
        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMM");
        Calendar cal=Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH,-1);
        String billMonth=sdf.format(cal.getTime());
        busiThirdSalesRoleUpd.setBillMonth(billMonth);
        BusiThirdSalesRoleUpd busiThirdSalesRoleUpdTemp = busiThirdSalesRoleUpd;
        List<ChannelSysBaseType> channelSysBaseTypeList = null;
        try{
            //查询审批状态配置
            ChannelSysBaseType channelSysBaseType = new ChannelSysBaseType();
            channelSysBaseType.setCodeType(10111);
            channelSysBaseType.setCodeId(busiThirdSalesRoleUpd.getAuditStatus());
            channelSysBaseTypeList = channelSysBaseTypeDao.query(channelSysBaseType);
            if (busiThirdSalesRoleUpd.getAgentAdjustUseId() != null){
                //查询审批人信息
                AgentAdjustUse adjustUse = new AgentAdjustUse();
                adjustUse.setAgentAdjustType(100);
                adjustUse.setAgentAdjustUseId(busiThirdSalesRoleUpd.getAgentAdjustUseId()); // 将id临时保存的，暂存
                List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(adjustUse);
                //1：属地市场部三级审批通过 -- > 待市场部职能审批   3：市场部职能审批通过 -- > 审核通过
                ////2：属地市场部三级审批驳回     4：市场部职能审批驳回
                if (busiThirdSalesRoleUpd.getAuditStatus() == 1){
                    busiThirdSalesRoleUpdTemp.setAgentAdjustUseId(agentAdjustUses.get(0).getAgentAdjustUseId());
                    busiThirdSalesRoleUpdTemp.setAgentAdjustUserName(agentAdjustUses.get(0).getAgentAdjustName());
                    busiThirdSalesRoleUpdTemp.setApproveStatus(channelSysBaseTypeList.get(0).getCodeName());
                }else{
                    busiThirdSalesRoleUpdTemp.setAgentAdjustUseId(null);
                    busiThirdSalesRoleUpdTemp.setAgentAdjustUserName(null);
                    busiThirdSalesRoleUpdTemp.setApproveStatus(channelSysBaseTypeList.get(0).getCodeName());
                }

                busiThirdSalesRoleUpdTemp.setDoneDate(new Date());
                busiThirdSalesRoleUpdDao.update(busiThirdSalesRoleUpdTemp);

                if (busiThirdSalesRoleUpd.getAuditStatus() == 1){
                    try{
                        //开始定义短信信息，并调接口发送至审批人手机
                        String name =sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-第三方直销角色调整申请属地审批-" +
                                DateUtil.formatDate(new Date(),"yyyyMMdd") + "-" + busiThirdSalesRoleUpd.getDoneCode();
                        String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为"+name+"";
                        logger.info(messager1);
                        String smsCode = "100011051243";
                        SPrivData sPrivData1 = sPrivData;
                        sPrivData1.setOpId(999990131L);
                        sPrivData1.setOrgId(0L);
                        channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);

                    }catch (Exception e){
                        logger.error("发送短信给审批人异常，失败原因是：" + e);
                    }
                }else{
                    Long doneCode = busiThirdSalesRoleUpd.getDoneCode();
                    busiThirdSalesRoleUpd = new BusiThirdSalesRoleUpd();
                    busiThirdSalesRoleUpd.setBillMonth(billMonth);
                    busiThirdSalesRoleUpd.setDoneCode(doneCode);
                    List<BusiThirdSalesRoleUpd> busiThirdSalesRoleUpdList = busiThirdSalesRoleUpdDao.query(busiThirdSalesRoleUpd);
                    if (busiThirdSalesRoleUpdList.size()>0){
                        for (BusiThirdSalesRoleUpd entity:busiThirdSalesRoleUpdList) {
                            //审批通过 修改网点的角色 获取审批信息中的调整信息更新进网点信息中
                            Long nodeId = entity.getNodeId();
                            Integer roleId = entity.getRoleId();
                            ChannelNode channelNode = new ChannelNode();
                            channelNode.setNodeId(nodeId);
                            channelNode.setBusiRole(roleId);
                            channelNode.setDoneDate(new Date());
                            channelNodeDao.updateBusiRole(channelNode);
                        }
                    }



                }
            }else{
                busiThirdSalesRoleUpdTemp.setApproveStatus(channelSysBaseTypeList.get(0).getCodeName());
                busiThirdSalesRoleUpdTemp.setDoneDate(new Date());

                busiThirdSalesRoleUpdDao.update(busiThirdSalesRoleUpdTemp);
            }

        }catch (Exception e){
            logger.error("修改数据失败，失败原因是："+e);
            throw new Exception("修改数据失败，失败原因" +e);
        }
    }

    @Override
    public PageData<ChannelNodeGainAssessManage> queryChannelNodeGainAssessManage(ChannelNodeGainAssessManage channelNodeGainAssessManage, PageParameter page) throws Exception {
        List<ChannelNodeGainAssessManage> channelNodeGainAssessManageList = null;
        try {
            channelNodeGainAssessManageList = channelNodeGainAssessManageDao.queryChannelNodeGainAssessInfo(channelNodeGainAssessManage,page);
            for (ChannelNodeGainAssessManage entity:channelNodeGainAssessManageList) {
                entity.getBillMonth();

            }
        }catch (Exception e){
            throw new Exception(e);
        }

        return new PageData<ChannelNodeGainAssessManage>(channelNodeGainAssessManageList,page);
    }

    @Override
    public void addChannelNodeGainAssessManage(ChannelNodeGainAssessManage channelNodeGainAssessManage) throws Exception{
        //渠道团队枚举转换
        List<ChannelSysBaseType> channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10063, channelNodeGainAssessManage.getNodeKind(), 1, null);
        if (channelSysBaseTypeList != null){
            channelNodeGainAssessManage.setKindName(channelSysBaseTypeList.get(0).getCodeName());
        }
        if (channelNodeGainAssessManage.getNodeId() != null){
            List<ChannelNodeDtl> channelNodeDtls = channelNodeDao.getChannelNodeInfo(channelNodeGainAssessManage.getNodeId());
            if (channelNodeDtls.size() == 1){
                ChannelNodeDtl channelNodeDtl = channelNodeDtls.get(0);
                String nodeName = channelNodeDtl.getChannelEntityName();
                channelNodeGainAssessManage.setNodeName(nodeName);
            }else {
                throw new Exception("所选择的网点匹配的信息不唯一，请确认！");
            }
        }
        //判断记录是否存在
        List<ChannelNodeGainAssessManage> channelNodeGainAssessManageList = channelNodeGainAssessManageDao.query(channelNodeGainAssessManage);
        if(channelNodeGainAssessManageList.size() > 0){
            throw new Exception("该网店月度营业厅虚拟利润考核已录入，请至营业厅虚拟利润考核管理界面查看。");
        }
        channelNodeGainAssessManage.setState(1);
        channelNodeGainAssessManage.setDoneDate(new Date());
        try{
            channelNodeGainAssessManageDao.insert(channelNodeGainAssessManage);
        }catch (Exception e){
            logger.error("保存失败，失败原因是:" + e);
            throw new Exception(e.getMessage());
        }


    }

    @Override
    public void delChannelNodeGainAssessManage(ChannelNodeGainAssessManage channelNodeGainAssessManage) throws Exception{
        try {
            channelNodeGainAssessManage.setState(0);
            channelNodeGainAssessManage.setDoneDate(new Date());
            channelNodeGainAssessManageDao.update(channelNodeGainAssessManage);
        }catch (Exception e){
            throw new Exception(e);
        }


    }

    @Override
    public List<List<Object>> dependencyBusiThirdXXJSJLInfo(List<List<Object>> list,String remoteFileName, String remotePath, SPrivData sPrivData,Long agentAdjustUseId) throws Exception {
        List<List<Object>> returnList = new ArrayList<List<Object>>();
        Date date = new Date();
        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMM");
        Calendar cal=Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH,-1);
        String billMonth=sdf.format(cal.getTime());
        Long orgId = sPrivData.getOrgId();
        String orgName = sPrivData.getOrgName();
        Long opId = sPrivData.getOpId();
        String opName = sPrivData.getOpName();

        try {
            logger.info("--------开始将属地第三方行销即时激励管理录入月份数据置失效---------");
            DependencyBusiThirdXXJSJL busiThirdXXJSJL = new DependencyBusiThirdXXJSJL();
            busiThirdXXJSJL.setRecStatus(0);
            busiThirdXXJSJL.setOrgId(orgId);
            busiThirdXXJSJL.setBillMonth(billMonth);
            dependencyBusiThirdXXJSJLDao.update(busiThirdXXJSJL);
            logger.info("--------将属地第三方行销即时激励管理录入月份数据置失效 完成---------");
        }catch (Exception e){
            logger.info("将属地第三方行销即时激励管理录入月份数据置失效失败，失败原因是：" + e.getMessage());
            throw new Exception("将属地第三方行销即时激励管理录入月份数据置失效失败，失败原因是：" + e.getMessage());
        }
        Long doneCode = dependencyBusiThirdXXJSJLDao.getSequence();
        //查询审批人信息
        AgentAdjustUse adjustUse = new AgentAdjustUse();
        adjustUse.setAgentAdjustType(2);
        adjustUse.setAgentAdjustUseId(agentAdjustUseId); // 将id临时保存的，暂存
        List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(adjustUse);
        if (agentAdjustUses.size() != 1){
            throw new Exception("属地第三方行销即时激励管理录入，所选择审批人不存在，请重新选择录入！");
        }
        adjustUse = agentAdjustUses.get(0);
        String agentAdjustUseName = adjustUse.getAgentAdjustName();


        String regNodeFee = "^\\d+(\\.\\d{1,2})?$";
        for (int i = 0; i < list.size(); i++) {
            List<Object> row = list.get(i);
            try {
                if (i == 0) {
                    row.add("结果");
                    row.add("备注");
                } else {
                    if (row.size() != 3) {
                        throw new Exception("列数不满足");
                    }
                    String nodeName = row.get(0) == null ? "" : row.get(0).toString().trim();
                    String gridName = row.get(1) == null ? "" : row.get(1).toString().trim();
                    String nodeFee = row.get(2) == null ? "" : row.get(2).toString().trim();
                    if (nodeName.equals("")) {
                        throw new Exception("格内小组长网点名称（兑付人）不能为空");
                    }
                    List<Map<String, Object>> nodeInfos = channelFuseItemDao.queryNodeByName(nodeName);
                    if (nodeInfos.size() < 1) {
                        throw new Exception("请确认该格内小组长网点名称（兑付人）是否存在、状态是否正常");
                    }
                    if (nodeInfos.size() > 1) {
                        throw new Exception("该格内小组长网点名称（兑付人）在系统中存在多条，请确认");
                    }
                    if (StringUtils.isNullOrBlank(gridName)){
                        throw new Exception("网格名称不能为空！！");
                    }else{
                        if (gridName.indexOf("alert")>-1 || gridName.indexOf("=")>-1){
                            throw new Exception("网格名称涉及敏感信息，请确认！！");
                        }
                    }
                    if (StringUtils.isNullOrBlank(nodeFee)){
                        throw new Exception("金额不能为空！！");
                    }else{
                        nodeFee = String.valueOf(((Double) row.get(2)).toString());
                        if (!nodeFee.matches(regNodeFee)){
                            throw new Exception("金额只能支持大于0的两位小数，请重新输入!");
                        }
                    }

                    Long nodeId = Long.valueOf(nodeInfos.get(0).get("CHANNEL_ENTITY_ID").toString());


                    DependencyBusiThirdXXJSJL dependencyBusiThirdXXJSJL = new DependencyBusiThirdXXJSJL();
                    dependencyBusiThirdXXJSJL.setBillMonth(billMonth);
                    dependencyBusiThirdXXJSJL.setNodeId(nodeId);
                    dependencyBusiThirdXXJSJL.setNodeName(nodeName);
                    dependencyBusiThirdXXJSJL.setGridName(gridName);
                    dependencyBusiThirdXXJSJL.setNodeFee(Double.parseDouble(nodeFee));
                    dependencyBusiThirdXXJSJL.setOpId(opId);
                    dependencyBusiThirdXXJSJL.setOpName(opName);
                    dependencyBusiThirdXXJSJL.setOrgId(orgId);
                    dependencyBusiThirdXXJSJL.setOrgName(orgName);
                    dependencyBusiThirdXXJSJL.setDoneDate(new Date());
                    dependencyBusiThirdXXJSJL.setFileName(remoteFileName);
                    dependencyBusiThirdXXJSJL.setFilePath(remotePath);
                    dependencyBusiThirdXXJSJL.setRecStatus(1);
                    dependencyBusiThirdXXJSJL.setDoneCode(doneCode);
                    dependencyBusiThirdXXJSJL.setAuditStatus(0);
                    dependencyBusiThirdXXJSJL.setApproveStatus("待属地市场部三级审批");
                    dependencyBusiThirdXXJSJL.setAgentAdjustUseId(agentAdjustUseId);
                    dependencyBusiThirdXXJSJL.setAgentAdjustUseName(agentAdjustUseName);
                    agentServiceFeeService.addDependencyBusiThirdXXJSJLInfoForBatch(dependencyBusiThirdXXJSJL);
                    row.add("成功");
                    row.add("");
                }
            } catch (Exception e) {
                logger.error("", e);
                while (row.size() < 1) {
                    row.add("");
                }
                row.add("失败");
                row.add(e.getMessage());
            } finally {
                returnList.add(row);
            }
        }
        try{
            //开始定义短信信息，并调接口发送至审批人手机
            String name =sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-属地第三方行销即时激励录入-" +
                    DateUtil.formatDate(new Date(),"yyyyMMdd") + "-" + doneCode;
            String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为"+name+"";
            logger.info(messager1);
            String smsCode = "100011051243";
            SPrivData sPrivData1 = sPrivData;
            sPrivData1.setOpId(999990131L);
            sPrivData1.setOrgId(0L);
            channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);

        }catch (Exception e){
            logger.error("发送短信给审批人异常，失败原因是：" + e);
        }

        return returnList;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public void addDependencyBusiThirdXXJSJLInfoForBatch(DependencyBusiThirdXXJSJL dependencyBusiThirdXXJSJL) throws Exception {
        //批量录入：覆盖信息（即批量导入信息直接覆盖系统内原信息）
        try {
            dependencyBusiThirdXXJSJLDao.insert(dependencyBusiThirdXXJSJL);
        }catch (Exception e){
            logger.info("录入数据异常，原因是：" + e);
            throw new Exception("录入数据异常，原因是：" + e);
        }

    }

    public PageData<DependencyBusiThirdXXJSJL> dependencyBusiThirdXXJSJLInfoQuery(DependencyBusiThirdXXJSJL dependencyBusiThirdXXJSJL,
                                                                                  PageParameter pageParameter) throws Exception{
        List<DependencyBusiThirdXXJSJL> dependencyBusiThirdXXJSJLList =null;
        if (StringUtils.isNotNullOrBlank(dependencyBusiThirdXXJSJL.getAgentAdjustUseName())){
            Long agentAdjustUseId = agentAdjustUseDao.getIdByUserName(dependencyBusiThirdXXJSJL.getAgentAdjustUseName());
            dependencyBusiThirdXXJSJL.setAgentAdjustUseId(agentAdjustUseId);
            dependencyBusiThirdXXJSJLList = dependencyBusiThirdXXJSJLDao.queryDependencyBusiThirdXXJSJLApproveInfo(dependencyBusiThirdXXJSJL, pageParameter);
            return new PageData<DependencyBusiThirdXXJSJL>(dependencyBusiThirdXXJSJLList, pageParameter.getTotalCount());
        }
        try {
            dependencyBusiThirdXXJSJLList = dependencyBusiThirdXXJSJLDao.queryDependencyBusiThirdXXJSJLInfo(dependencyBusiThirdXXJSJL, pageParameter);
        }catch (Exception e){
            throw new Exception("" + e);
        }

        return new PageData<DependencyBusiThirdXXJSJL>(dependencyBusiThirdXXJSJLList, pageParameter);
    }


    @Override
    public void dependencyBusiThirdXXJSJLEdit(DependencyBusiThirdXXJSJL dependencyBusiThirdXXJSJL,SPrivData sPrivData) throws Exception {
        //因审批和修改走同一套逻辑 故判断其控制层如果没有传 状态 则状态给-1：失效  传了状态直接给 2：审批通过 或 3：审批驳回
        //对传了状态的数据 直接进行修改，而数据修改的 需要把原先数据置失效，再添加数据
        Date date = new Date();
        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMM");
        Calendar cal=Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH,-1);
        String billMonth=sdf.format(cal.getTime());
        dependencyBusiThirdXXJSJL.setBillMonth(billMonth);
        DependencyBusiThirdXXJSJL dependencyBusiThirdXXJSJLTemp = dependencyBusiThirdXXJSJL;
        List<ChannelSysBaseType> channelSysBaseTypeList = null;
        try{
            //查询审批状态配置
            ChannelSysBaseType channelSysBaseType = new ChannelSysBaseType();
            channelSysBaseType.setCodeType(10112);
            channelSysBaseType.setCodeId(dependencyBusiThirdXXJSJL.getAuditStatus());
            channelSysBaseTypeList = channelSysBaseTypeDao.query(channelSysBaseType);
            if (dependencyBusiThirdXXJSJL.getAgentAdjustUseId() != null){
                //查询审批人信息
                AgentAdjustUse adjustUse = new AgentAdjustUse();
                adjustUse.setAgentAdjustType(3);
                adjustUse.setAgentAdjustUseId(dependencyBusiThirdXXJSJL.getAgentAdjustUseId()); // 将id临时保存的，暂存
                List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(adjustUse);
                //1：属地市场部三级审批通过 -- > 待市场部职能审批   3：市场部职能审批通过 -- > 审核通过
                ////2：属地市场部三级审批驳回     4：市场部职能审批驳回
                if (dependencyBusiThirdXXJSJL.getAuditStatus() == 1){
                    dependencyBusiThirdXXJSJLTemp.setAgentAdjustUseId(agentAdjustUses.get(0).getAgentAdjustUseId());
                    dependencyBusiThirdXXJSJLTemp.setAgentAdjustUseName(agentAdjustUses.get(0).getAgentAdjustName());
                    dependencyBusiThirdXXJSJLTemp.setApproveStatus(channelSysBaseTypeList.get(0).getCodeName());
                }else{
                    dependencyBusiThirdXXJSJLTemp.setAgentAdjustUseId(null);
                    dependencyBusiThirdXXJSJLTemp.setAgentAdjustUseName(null);
                    dependencyBusiThirdXXJSJLTemp.setApproveStatus(channelSysBaseTypeList.get(0).getCodeName());
                }

                dependencyBusiThirdXXJSJLTemp.setDoneDate(new Date());
                dependencyBusiThirdXXJSJLDao.updateDependencyJSJLInfo(dependencyBusiThirdXXJSJLTemp);

                if (dependencyBusiThirdXXJSJL.getAuditStatus() == 1){
                    try{
                        //开始定义短信信息，并调接口发送至审批人手机
                        String name =sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-属地第三方行销即时激励审批-" +
                                DateUtil.formatDate(new Date(),"yyyyMMdd") + "-" + dependencyBusiThirdXXJSJL.getDoneCode();
                        String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为"+name+"";
                        logger.info(messager1);
                        String smsCode = "100011051243";
                        SPrivData sPrivData1 = sPrivData;
                        sPrivData1.setOpId(999990131L);
                        sPrivData1.setOrgId(0L);
                        channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);

                    }catch (Exception e){
                        logger.error("发送短信给审批人异常，失败原因是：" + e);
                    }
                }
            }else{
                dependencyBusiThirdXXJSJLTemp.setApproveStatus(channelSysBaseTypeList.get(0).getCodeName());
                dependencyBusiThirdXXJSJLTemp.setDoneDate(new Date());

                dependencyBusiThirdXXJSJLDao.updateDependencyJSJLInfo(dependencyBusiThirdXXJSJLTemp);
            }

        }catch (Exception e){
            logger.error("修改数据失败，失败原因是："+e);
            throw new Exception("修改数据失败，失败原因" +e);
        }
    }
    @Override
    public void addBusiThirdSetDateInfo(BusiThirdSetDate busiThirdSetDate) throws Exception {
        //判断记录是否存在
        List<BusiThirdSetDate> busiThirdSetDateList = busiThirdSetDateDao.query(busiThirdSetDate);
        try {
            if (busiThirdSetDateList.size() > 0) {
                busiThirdSetDate.setRecStatus(0);
                busiThirdSetDateDao.update(busiThirdSetDate);
            }
            busiThirdSetDate.setRecStatus(1);
            busiThirdSetDate.setDoneDate(new Date());
            busiThirdSetDateDao.insert(busiThirdSetDate);
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }

    }

    public PageData<BusiThirdSetDate> busiThirdSetDateInfoQuery(BusiThirdSetDate busiThirdSetDate,
                                                                PageParameter pageParameter) throws Exception{
        List<BusiThirdSetDate> busiThirdSetDateList =null;
        try {
            busiThirdSetDateList = busiThirdSetDateDao.queryBusiThirdSetDateInfo(busiThirdSetDate, pageParameter);
        }catch (Exception e){
            throw new Exception("" + e);
        }

        return new PageData<BusiThirdSetDate>(busiThirdSetDateList, pageParameter);
    }

    @Override
    public void executeXXAssessDate() throws Exception {
        Date date = new Date();
        SimpleDateFormat adf =new SimpleDateFormat("yyyyMM");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-1);
        date = calendar.getTime();
        String billMonth = adf.format(date);
        //查询当月有无数据，如果有则不再执行
        RwdTpmsRegionAward rwdTpmsRegionAward = new RwdTpmsRegionAward();
        rwdTpmsRegionAward.setAwardMonth(billMonth);
        List<RwdTpmsRegionAward> rwdTpmsRegionAwards = null;
        try {
            rwdTpmsRegionAwards = rwdTpmsRegionAwardDao.query(rwdTpmsRegionAward);
            if(CollectionUtils.isEmpty(rwdTpmsRegionAwards)){
                String sql = "select award_id,region_code,region_name,award_month,award_type,award_state,award_total_fee,manage_count,\n" +
                        "award_base_fee,base_ratio,award_sale_fee,sale_ratio,special_award,fine_award,create_date,done_date\n" +
                        "from SETTLE.RWD_TPMS_REGION_AWARD a where a.award_month = " + billMonth;
                logger.info(sql);
                Connection conn = null;
                conn = this.getConnection();
                Statement state = conn.createStatement();
                logger.info("=============开始查询酬金第三方直销人员校验费用数据================");
                ResultSet rs = state.executeQuery(sql);
                logger.info("取酬金库第三方直销人员校验费用数据开始入库！！！");
                while (rs.next()) {
                    Long awardId = Long.parseLong(rs.getObject(1) == null ? null : rs.getString(1));
                    Long regionCode = Long.parseLong(rs.getObject(2) == null ? null : rs.getString(2).trim());
                    String regionName= rs.getObject(3) == null ? null : rs.getString(3).trim();
                    String awardType=rs.getObject(5) == null ? null : rs.getString(5).trim();
                    String awardState=rs.getObject(6) == null ? null : rs.getString(6).trim();
                    String awardTotalFee=rs.getObject(7) == null ? null : rs.getString(7).trim();
                    String manageCount=rs.getObject(8) == null ? null : rs.getString(8).trim();
                    String awardBaseFee=rs.getObject(9) == null ? null : rs.getString(9).trim();
                    String baseRatio=rs.getObject(10) == null ? null : rs.getString(10).trim();
                    String awardSaleFee=rs.getObject(11) == null ? null : rs.getString(11).trim();
                    String saleRatio=rs.getObject(12) == null ? null : rs.getString(12).trim();
                    String specialAward=rs.getObject(13) == null ? null : rs.getString(13).trim();
                    String fineAward=rs.getObject(14) == null ? null : rs.getString(14).trim();
                    String createDate=rs.getObject(15) == null ? null : rs.getString(15).trim();
                    String doneDate=rs.getObject(16) == null ? null : rs.getString(16).trim();
                    rwdTpmsRegionAward.setAwardId(awardId);
                    rwdTpmsRegionAward.setRegionCode(regionCode);
                    rwdTpmsRegionAward.setRegionName(regionName);
                    rwdTpmsRegionAward.setAwardType(awardType);
                    rwdTpmsRegionAward.setAwardState(awardState);
                    rwdTpmsRegionAward.setAwardTotalFee(awardTotalFee);
                    rwdTpmsRegionAward.setManageCount(manageCount);
                    rwdTpmsRegionAward.setAwardBaseFee(awardBaseFee);
                    rwdTpmsRegionAward.setBaseRatio(baseRatio);
                    rwdTpmsRegionAward.setAwardSaleFee(awardSaleFee);
                    rwdTpmsRegionAward.setSaleRatio(saleRatio);
                    rwdTpmsRegionAward.setSpecialAward(specialAward);
                    rwdTpmsRegionAward.setFineAward(fineAward);
                    rwdTpmsRegionAward.setCreateDate(createDate);
                    rwdTpmsRegionAward.setDoneDate(doneDate);
                    try {
                        rwdTpmsRegionAwardDao.insert(rwdTpmsRegionAward);
                        logger.info("第三方直销人员校验费用数据入库成功！！！");
                    } catch (Exception e) {
                        logger.error("第三方直销人员校验费用数据入库失败！！！",e);
                    }
                }
            }else {
                logger.info("第三方直销人员校验费用数据入库失败：" + billMonth + "从酬金库获取每月新增的第三方直销人员校验费用数据导入渠道库已经执行过！");
            }
        }catch (Exception e){
            logger.info("查询渠道本地数据失败，原因是："+ e,e);
        }
    }

    public Connection getConnection() throws Exception {
        Connection conncection = null;
        String driver = "";
        String url = "";
        String user = "";
        String password = "";
        try {
            List<ChannelSysBaseType> channelSysBaseTypeList = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(50100, null, 1, null);
            if (CollectionUtils.isEmpty(channelSysBaseTypeList)) {
                throw new Exception("在库中未查询到关于新酬金库的相关配置！");
            }
            for (ChannelSysBaseType channelSysBaseType : channelSysBaseTypeList) {
                if ("driverClass".equals(channelSysBaseType.getCodeName())) {
                    driver = channelSysBaseType.getCodeNameNls();
                }
                if ("jdbcUrl".equals(channelSysBaseType.getCodeName())) {
                    url = channelSysBaseType.getCodeNameNls();
                }
                if ("user".equals(channelSysBaseType.getCodeName())) {
                    user = channelSysBaseType.getCodeNameNls();
                }
                if ("password".equals(channelSysBaseType.getCodeName())) {
                    password = channelSysBaseType.getCodeNameNls();
                }
            }

            Class.forName(driver);
            conncection = DriverManager.getConnection(url, user, password);
            logger.info("创建数据库连接成功!!");
        } catch (Exception e) {
            logger.error("创建数据库连接失败!!", e);
            throw e;
        }
        return conncection;
    }


    @Override
    public List<List<Object>> busiThirdPriceServiceRatioAdd(List<List<Object>> list, SPrivData sPrivData) throws Exception {
        List<List<Object>> returnList = new ArrayList<List<Object>>();
        Date date = new Date();
        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMM");
        Calendar cal=Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH,1);
        String billMonth=sdf.format(cal.getTime());
        Long orgId =sPrivData.getOrgId();
        String orgName= sPrivData.getOrgName();
        Long opId = sPrivData.getOpId();
        logger.info("开始覆盖该属地下，当月的所有数据，没有则不修改！！");
        BusiThirdPriceServiceRatio busiThirdPriceServiceRatio = new BusiThirdPriceServiceRatio();
        busiThirdPriceServiceRatio.setBillMonth(billMonth);
        busiThirdPriceServiceRatio.setOrgId(orgId);
        busiThirdPriceServiceRatio.setRecStatus(0);
        try {
            busiThirdPriceServiceRatioDao.update(busiThirdPriceServiceRatio);
        }catch (Exception e){
            throw new Exception(e);
        }

        logger.error("覆盖该属地下，当月的所有数据，没有则不修改！！");
        String reg = "^(?:0(?:\\.\\d{1,2})?|1(?:\\.0{1,2})?)$";
        if (list.size() != 16){
            logger.error("");
            throw new Exception("请导入正确格式的属地第三方直销服务单价自定义批量录入模板！！");
        }

        for (int i = 0; i < list.size(); i++) {
            List<Object> row = list.get(i);
            try {
                if (i == 0) {
                    row.add("结果");
                    row.add("备注");
                } else {
                    if (row.size() != 2) {
                        throw new Exception("列数不满足");
                    }
                    BusiThirdPriceServiceRatio busiThirdPriceServiceRatio1 = new BusiThirdPriceServiceRatio();
                    String busiType = row.get(0) == null ? "" : row.get(0).toString();
                    if (StringUtils.isNullOrBlank(busiType)){
                        throw new Exception("业务字段为必填，请重新录入！！！");
                    }else{
                        if (!SafetyFlewUtils.checkFileAttrUpload(busiType)){
                            throw new Exception("属地第三方直销服务单价自定义业务信息涉及敏感信息，请重新shuru属地第三方直销服务单价自定义");
                        }
                    }
                    List<ChannelSysBaseType> channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10064, null, 1, null);
                    if (channelSysBaseTypeList.size()>0){
                        Boolean flag = false;
                        for (ChannelSysBaseType entity:channelSysBaseTypeList) {
                            String codeName = entity.getCodeName();
                            if (busiType.equals(codeName)){
                                flag = true;
                                break;
                            }
                        }
                        if (!flag){
                            throw new Exception(busiType + "业务不存在，请检查！！！");
                        }
                    }

                    String customRatio = row.get(1) == null ? "" : row.get(1).toString();
                    if (StringUtils.isNotNullOrBlank(customRatio)){
                        if (!customRatio.matches(reg)){
                            throw new Exception("属地自定义系数设定范围在[0,1]之间且最多输入两位小数，请重新录入！！！");
                        }
                    }else{
                        throw new Exception("属地自定义系数设定字段为必填，请重新录入！！！");
                    }
                    busiThirdPriceServiceRatio1.setBusiType(busiType);
                    busiThirdPriceServiceRatio1.setCustomRatio(customRatio);
                    busiThirdPriceServiceRatio1.setOpId(opId);
                    busiThirdPriceServiceRatio1.setOrgId(orgId);
                    busiThirdPriceServiceRatio1.setOrgName(orgName);
                    busiThirdPriceServiceRatio1.setBillMonth(billMonth);
                    busiThirdPriceServiceRatio1.setDoneDate(new Date());
                    busiThirdPriceServiceRatio1.setRecStatus(1);
                    busiThirdPriceServiceRatioDao.insert(busiThirdPriceServiceRatio1);
                    row.add("成功");
                    row.add("");
                }
            } catch (Exception e) {
                logger.error("", e);
                while (row.size() < 1) {
                    row.add("");
                }
                row.add("失败");
                row.add(e.getMessage());
            } finally {
                returnList.add(row);
            }
        }

        return returnList;
    }

    public PageData<BusiThirdPriceServiceRatio> busiThirdPriceServiceRatioInfoQuery(BusiThirdPriceServiceRatio busiThirdPriceServiceRatio,
                                                                                    PageParameter pageParameter) throws Exception{
        List<BusiThirdPriceServiceRatio> busiThirdSetDateList =null;
        try {
            busiThirdSetDateList = busiThirdPriceServiceRatioDao.queryInfo(busiThirdPriceServiceRatio, pageParameter);
        }catch (Exception e){
            throw new Exception("" + e);
        }

        return new PageData<BusiThirdPriceServiceRatio>(busiThirdSetDateList, pageParameter);
    }

    @Override
    public List<List<Object>> busiThirdSpecialOfferInfoAdd(List<List<Object>> lists, SPrivData sPrivData) throws Exception {
        List<List<Object>> returnList = new ArrayList<List<Object>>();
        Date date = new Date();
        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMM");
        Calendar cal=Calendar.getInstance();
        cal.setTime(date);
//        cal.add(Calendar.MONTH,1);
        String billMonths=sdf.format(cal.getTime());
        Long billMonth = Long.parseLong(billMonths);
        Long orgId =sPrivData.getOrgId();
        String orgName= sPrivData.getOrgName();
        Long opId = sPrivData.getOpId();
        logger.info("开始覆盖该属地下，当月的所有数据，没有则不修改！！");
        BusiThirdSpecialOfferInfo busiThirdSpecialOfferInfo = new BusiThirdSpecialOfferInfo();
        busiThirdSpecialOfferInfo.setBillMonth(billMonth);
        busiThirdSpecialOfferInfo.setOrgId(orgId);
        busiThirdSpecialOfferInfo.setRecStatus(0);
        try {
            busiThirdSpecialOfferInfoDao.update(busiThirdSpecialOfferInfo);
        }catch (Exception e){
            throw new Exception(e);
        }

        logger.error("覆盖该属地下，当月的所有数据，没有则不修改！！");
        String reg = "^\\d+(\\.\\d{1,2})?$";
        if (lists.size() == 1){
            logger.error("");
            throw new Exception("请导入正确格式的集团成员个性化资费录入模板！！");
        }
        Long doneCode = busiThirdSpecialOfferInfoDao.getSequence();

        for (int i = 0; i < lists.size(); i++) {
            List<Object> row = lists.get(i);
            try {
                if (i == 0) {
                    row.add("结果");
                    row.add("备注");
                } else {
                    if (row.size() != 4) {
                        throw new Exception("列数不满足");
                    }
                    String offerDate = row.get(0) == null ? "" : row.get(0).toString().trim();
                    String offerId = row.get(1) == null ? "" : row.get(1).toString().trim();
                    String offerName = row.get(2) == null ? "" : row.get(2).toString().trim();
                    String offerFee = row.get(3) == null ? "" : row.get(3).toString().trim();
                    
                    if (StringUtils.isNullOrBlank(offerId)){
                        throw new Exception("策划id不能为空！！");
                    }else{
                        if (offerId.length()<12){
                            throw new Exception("crm的策划id应该为12位数字，请确认！！");
                        }
                    }
                    if (StringUtils.isNullOrBlank(offerFee)){
                        throw new Exception("金额不能为空！！");
                    }else{
                        offerFee = String.valueOf(((Double) row.get(3)).toString());
                        if (!offerFee.matches(reg)){
                            throw new Exception("金额只能支持大于0的两位小数，请重新输入!");
                        }
                    }
                    
                    BusiThirdSpecialOfferInfo busiThirdSpecialOfferInfo1 = new BusiThirdSpecialOfferInfo();
                    busiThirdSpecialOfferInfo1.setOfferId(Long.parseLong(offerId));
                    busiThirdSpecialOfferInfo1.setOfferName(offerName);
                    busiThirdSpecialOfferInfo1.setOfferDate(Long.parseLong(offerDate));
                    busiThirdSpecialOfferInfo1.setSpecialFee(Double.parseDouble(offerFee));
                    busiThirdSpecialOfferInfo1.setOpId(opId);
                    busiThirdSpecialOfferInfo1.setOrgId(orgId);
                    busiThirdSpecialOfferInfo1.setBillMonth(billMonth);
                    busiThirdSpecialOfferInfo1.setDoneDate(new Date());
                    busiThirdSpecialOfferInfo1.setRecStatus(1);
                    busiThirdSpecialOfferInfo1.setAuditStatus(1);
                    busiThirdSpecialOfferInfo1.setDoneCode(doneCode);
                    busiThirdSpecialOfferInfoDao.insert(busiThirdSpecialOfferInfo1);
                    row.add("成功");
                    row.add("");
                }
            } catch (Exception e) {
                logger.error("", e);
                while (row.size() < 1) {
                    row.add("");
                }
                row.add("失败");
                row.add(e.getMessage());
            } finally {
                returnList.add(row);
            }
        }

        return returnList;
    }

    @Override
    public PageData<BusiThirdSpecialOfferInfo> busiThirdSpecialOfferInfoQuery(BusiThirdSpecialOfferInfo busiThirdSpecialOfferInfo, PageParameter page) throws Exception {
        List<BusiThirdSpecialOfferInfo> busiThirdSpecialOfferList =null;
        try {
            busiThirdSpecialOfferList = busiThirdSpecialOfferInfoDao.queryInfo(busiThirdSpecialOfferInfo, page);
        }catch (Exception e){
            throw new Exception("" + e);
        }

        return new PageData<BusiThirdSpecialOfferInfo>(busiThirdSpecialOfferList, page);
    }


    @Override
    public void addTietongOnlineEval(TietongOnlineEval tietongOnlineEval) throws Exception {
        try {

            tietongOnlineEval.setRecStatus(1);
            tietongOnlineEval.setDoneDate(new Date());
            tietongOnlineEvalDao.insert(tietongOnlineEval);

        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public PageData<TietongOnlineEval> queryTietongOnlineEval(TietongOnlineEval tietongOnlineEval, PageParameter page) throws Exception {
        List<TietongOnlineEval> busiThirdSetDateList;
        try {
            busiThirdSetDateList = tietongOnlineEvalDao.query(tietongOnlineEval, page);
        } catch (Exception e) {
            throw e;
        }

        return new PageData<TietongOnlineEval>(busiThirdSetDateList, page);
    }

    @Override
    public void editTietongOnlineEval(TietongOnlineEval tietongOnlineEval) throws Exception {
        try {
            tietongOnlineEval.setDoneDate(new Date());
            tietongOnlineEvalDao.update(tietongOnlineEval);
        } catch (Exception e) {
            throw e;
        }
    }

    //第三方行销项目支撑费录入
    @Override
    public void addTieTongSupportFee(TieTongSupportFeeAdd tieTongSupportFeeAdd ,SPrivData sPrivData) throws Exception{
//        List<TieTongSupportFeeAdd> threeSupportFeeAddList = tieTongSupportFeeAddDao.query(tieTongSupportFeeAdd);
//        if(threeSupportFeeAddList.size()>0){
//            throw new Exception("该条记录已录入，请勿重复添加");
//        }

        if (!SafetyFlewUtils.checkFileAttrUpload(tieTongSupportFeeAdd.getSupportRemark())){
            throw new Exception("备注字段涉及敏感信息，请重新输入！");
        }
        //市场-2025-5712_渠道二期新增集团成员个性化资费录入菜单 清理过期数据
        TieTongSupportFeeAdd tieTongSupportFeeDel = new TieTongSupportFeeAdd();
        tieTongSupportFeeDel.setSupportTime(tieTongSupportFeeAdd.getSupportTime());
        tieTongSupportFeeDel.setOrgId(tieTongSupportFeeAdd.getOrgId());
        tieTongSupportFeeDel.setRecStatus(-1);
        tieTongSupportFeeAddDao.update(tieTongSupportFeeDel);
        
        //获取序列号
        Long doneCode = tieTongSupportFeeAddDao.getSequence();
        try {
            tieTongSupportFeeAdd.setRecStatus(0);
            tieTongSupportFeeAdd.setDoneDate(new Date());
            tieTongSupportFeeAdd.setDoneCode(doneCode);
            tieTongSupportFeeAddDao.insert(tieTongSupportFeeAdd);
        }catch (Exception e){
            logger.error("插入数据库异常");
        }
        try{
            //查询审批人信息
            AgentAdjustUse adjustUse = new AgentAdjustUse();
            adjustUse.setAgentAdjustType(101);
            adjustUse.setAgentAdjustUseId(tieTongSupportFeeAdd.getAgentAdjustUseId()); // 将id临时保存的，暂存
            List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(adjustUse);
            //开始定义短信信息，并调接口发送至审批人手机
            String name =sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-属地铁通直销项目费用调整审批-" +
                    DateUtil.formatDate(new Date(),"yyyyMMdd") + "-" + doneCode;
            String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为"+name+"";
            logger.info(messager1);
            String smsCode = "100011051243";
            SPrivData sPrivData1 = sPrivData;
            sPrivData1.setOpId(999990131L);
            sPrivData1.setOrgId(0L);
            channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);

        }catch (Exception e){
            logger.error("发送短信给审批人异常，失败原因是：" + e);
        }

    }

    /**
     * <p> 属地铁通直销项目费用调整
     * <p> 管理界面查询按钮
     */
    @Override
    public PageData<TieTongSupportFeeAdd> ManageTieTongSupportFee(String begUpLoadDate, String endUpLoadDate, PageParameter page) {
        List<TieTongSupportFeeAdd> tieTongSupportFeeAddList = tieTongSupportFeeAddDao.queryTieTongSupportFeeAdd(begUpLoadDate, endUpLoadDate, page);
        if (tieTongSupportFeeAddList.size() > 0) {
            for (TieTongSupportFeeAdd returnData : tieTongSupportFeeAddList) {
                this.setStatusName_AndAuditPersonName(returnData);
            }
        }
        return new PageData<TieTongSupportFeeAdd>(tieTongSupportFeeAddList, page);
    }

    /**
     * 设置下一个审批人的名字、审批状态的中文名
     */
    private void setStatusName_AndAuditPersonName(TieTongSupportFeeAdd returnData) {

        if (returnData.getRecStatus() == 0) {
            returnData.setRecStatusApprove("待属地市场部三级审批");
            this.setAdjustUseName(returnData);

        } else if (returnData.getRecStatus() == 1) {
            returnData.setRecStatusApprove("待属地市场部二级审批");
            this.setAdjustUseName(returnData);

        } else if (returnData.getRecStatus() == 2) {
            returnData.setRecStatusApprove("属地市场部三级审批拒绝");
        } else if (returnData.getRecStatus() == 3) {
            returnData.setRecStatusApprove("审批通过");
        } else if (returnData.getRecStatus() == 4) {
            returnData.setRecStatusApprove("属地市场部二级审批拒绝");
        }
    }

    private void setAdjustUseName(TieTongSupportFeeAdd returnData) {
        AgentAdjustUse adjustUse = new AgentAdjustUse();
        adjustUse.setAgentAdjustUseId(returnData.getAgentAdjustUseId());
        List<AgentAdjustUse> adjustUseList = agentAdjustUseDao.query(adjustUse);

        if (adjustUseList.size() > 0) {
            returnData.setAgentAdjustUseName(adjustUseList.get(0).getAgentAdjustName());
        } else {
            // 为避免报错，不设置审批人名称
        }
    }

    private void setAdjustUseName(BusiThirdXXAssess returnData) {
        AgentAdjustUse adjustUse = new AgentAdjustUse();
        adjustUse.setAgentAdjustUseId(returnData.getAgentAdjustUseId());
        List<AgentAdjustUse> adjustUseList = agentAdjustUseDao.query(adjustUse);

        if (adjustUseList.size() > 0) {
            returnData.setAgentAdjustUseName(adjustUseList.get(0).getAgentAdjustName());
        } else {
            // 为避免报错，不设置审批人名称
        }
    }

    /**
     * <p> 属地铁通直销项目费用调整
     * <p> 审批界面查询按钮
     */
    @Override
    public PageData<TieTongSupportFeeAdd> tieTongSupportFeeInfoQuery(TieTongSupportFeeAdd tieTongSupportFeeAdd, PageParameter pageParameter) {
        List<TieTongSupportFeeAdd> tieTongSupportFeeAddList;
        try {
            // 获取本操作员信息，查询该操作员应该审批的单子
            String userName = tieTongSupportFeeAdd.getUserName();
            Long agentAdjustUseId = agentAdjustUseDao.getIdByUserName(userName);
            if (agentAdjustUseId != null){
                tieTongSupportFeeAdd.setAgentAdjustUseId(agentAdjustUseId);
            }else{
                logger.info("没有该审批人的信息，返回0个审批单！");
                return new PageData<TieTongSupportFeeAdd>(null, pageParameter);
            }
            tieTongSupportFeeAddList = tieTongSupportFeeAddDao.queryTieTongSupportFeeAddInfo(tieTongSupportFeeAdd, pageParameter);
            if (tieTongSupportFeeAddList.size()>0){
                for (TieTongSupportFeeAdd entity:tieTongSupportFeeAddList) {
                    this.setStatusName_AndAuditPersonName(entity);
                }
            }
            return new PageData<TieTongSupportFeeAdd>(tieTongSupportFeeAddList, pageParameter.getTotalCount());
        }catch (Exception e){
            logger.error("属地铁通直销项目费用调整-审批查询异常：", e);
        }
        return new PageData<TieTongSupportFeeAdd>(null, pageParameter.getTotalCount());
    }

    @Override
    public void tieTongSupportFee_AddOrEdit(TieTongSupportFeeAdd tieTongSupportFeeAdd, SPrivData sPrivData) throws Exception {
        //因审批和修改走同一套逻辑 故判断其控制层如果没有传 状态 则状态给-1：失效  传了状态直接给 2：审批通过 或 3：审批驳回
        //对传了状态的数据 直接进行修改，而数据修改的 需要把原先数据置失效，再添加数据

        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.MONTH, -1);
        String billMonthMinus1 = new SimpleDateFormat("yyyyMM").format(cal.getTime());

        try {
            tieTongSupportFeeAdd.setDoneDate(new Date());

            if (tieTongSupportFeeAdd.getRecStatus() == null
                    || tieTongSupportFeeAdd.getRecStatus() == -1) { // 新增逻辑
                tieTongSupportFeeAddDao.insert(tieTongSupportFeeAdd);

            } else { // 更新逻辑
                tieTongSupportFeeAdd.setSupportTime(billMonthMinus1);
                tieTongSupportFeeAddDao.update(tieTongSupportFeeAdd);
            }

            sendSms: if (tieTongSupportFeeAdd.getRecStatus() == 1) { // 变更后是1的话，说明刚审批了一级，此时发送短信给第二级
                // 查询审批人信息
                AgentAdjustUse adjustUse = new AgentAdjustUse();
                adjustUse.setAgentAdjustType(101);
                adjustUse.setAgentAdjustUseId(tieTongSupportFeeAdd.getAgentAdjustUseId()); // 将id临时保存的，暂存
                List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(adjustUse);
                if (agentAdjustUses.isEmpty()) {
                    break sendSms;
                }

                // 开始定义短信信息，并调接口发送至审批人手机
                String name = sPrivData.getOrgName() + "-" + sPrivData.getOpName() + "-属地铁通直销项目费用调整审批-" +
                        DateUtil.formatDate(new Date(), "yyyyMMdd") + "-" + tieTongSupportFeeAdd.getDoneCode();
                String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为" + name + "";
                logger.info(messager1);
                String smsCode = "100011051243";
                SPrivData sPrivData1 = sPrivData;
                sPrivData1.setOpId(999990131L);
                sPrivData1.setOrgId(0L);
                channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);
            }

        } catch (Exception e) {
            logger.error("修改数据失败，失败原因是：", e);
            throw new Exception("修改数据失败，失败原因：" + ExceptionUtil.buildMessage(e));
        }
    }

    @Override
    public void busiThirdDependRoleAdd(BusiThirdDependRole busiThirdDependRole) throws Exception{
        Long userId = busiThirdDependRoleDao.getSequence();
        if (StringUtils.isNullOrBlank(busiThirdDependRole.getUserName().trim())){
            throw new Exception("姓名不能为空，请重新输入！！");
        }
        if (!SafetyFlewUtils.checkFileAttrUpload(busiThirdDependRole.getUserName())){
            throw new Exception("姓名涉及敏感信息，请重新输入！！");
        }
        try {
            busiThirdDependRole.setUserId(userId);
            List<ChannelSysBaseType> channelSysBaseTypeList =
                    ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(50445, Integer.parseInt(busiThirdDependRole.getOrgId().toString()), 0, null);
            if (channelSysBaseTypeList.size() ==1 ){
                busiThirdDependRole.setOrgName(channelSysBaseTypeList.get(0).getCodeName());
            }
            busiThirdDependRole.setDoneDate(new Date());
            busiThirdDependRole.setRecStatus(1);
            busiThirdDependRoleDao.insert(busiThirdDependRole);
        }catch (Exception e){
            throw new Exception(e);
        }
    }

    @Override
    public PageData<BusiThirdDependRole> busiThirdDependRoleManage(Long orgId, PageParameter page) throws Exception{
        BusiThirdDependRole busiThirdDependRole = new BusiThirdDependRole();
        busiThirdDependRole.setRecStatus(1);
        busiThirdDependRole.setOrgId(orgId);
        List<BusiThirdDependRole> busiThirdDependRoleList = busiThirdDependRoleDao.queryBusiThirdDependRole(busiThirdDependRole, page);
        return new PageData<BusiThirdDependRole>(busiThirdDependRoleList, page);
    }

    @Override
    public void busiThirdDependRoleEdit(BusiThirdDependRole busiThirdDependRole) throws Exception {
        try{
            busiThirdDependRoleDao.update(busiThirdDependRole);
        }catch (Exception e){
            logger.error(""+e);
        }
    }
    public void sendMessageForDependRole() throws Exception{
        logger.info("开始对第三方直销属地职能管理未打分，按属地进行短信提醒");
        List<ChannelSysBaseType> channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(50445, null, 0, null);
        if (channelSysBaseTypeList.size()>0){
            List<List<Object>> returnList = new ArrayList<List<Object>>();
            Date date = new Date();
            SimpleDateFormat sdf =new SimpleDateFormat("yyyyMM");
            Calendar cal=Calendar.getInstance();
            cal.setTime(date);
            cal.add(Calendar.MONTH,-1);
            String billMonth=sdf.format(cal.getTime());
            for (int i = 0; i < channelSysBaseTypeList.size(); i++) {
                String orgName  =channelSysBaseTypeList.get(i).getCodeName().trim();
                Long orgId = Long.parseLong(channelSysBaseTypeList.get(i).getCodeId().toString());
                if (orgName.contains("分公司") && orgId != null){
                    BusiThirdXXAssess busiThirdXXAssess = new BusiThirdXXAssess();
                    busiThirdXXAssess.setOrgId(orgId);
                    busiThirdXXAssess.setBillMonth(billMonth);
                    List<BusiThirdXXAssess> busiThirdXXAssessList = busiThirdXXAssessDao.query(busiThirdXXAssess);
                    if (busiThirdXXAssessList.size()<1){
                        BusiThirdDependRole busiThirdDependRole = new BusiThirdDependRole();
                        busiThirdDependRole.setOrgId(orgId);
                        List<BusiThirdDependRole> busiThirdDependRoleList = busiThirdDependRoleDao.query(busiThirdDependRole);
                        if (busiThirdDependRoleList.size()>0){
                            logger.info(billMonth + "月份，属地Id为：" + orgId +"，无阅读考核数据，开始从职能管理信息表读取属地信息发送短信！！");
                            for (BusiThirdDependRole entity:busiThirdDependRoleList) {
                                String year = billMonth.substring(0,4);
                                String month = billMonth.substring(4);
                                String userName = entity.getUserName();
                                //开始定义短信信息，并调接口发送至审批人手机
                                String name =year +"年"+ month+"月"+"属地第三方人员月度考核及相关费用录入尚未提交审批，14号24点之前无法完成审批,将默认0分及无调整费，请尽快完成。";
                                logger.info("开始给" + userName + "发送短信，短信内容未:" + name);
                                String smsCode = "100011061556";
                                SPrivData sPrivData1 = new SPrivData();
                                sPrivData1.setOpId(999990131L);
                                sPrivData1.setOrgId(0L);
                                try {
                                    channelNotifyService.sendSMSMessage(smsCode, entity.getPhoneNo().toString(), name, DateUtil.getCurrDate(), sPrivData1);

                                }catch (Exception e){
                                    logger.error(""+e);
                                }

                            }
                        }
                    }

                }
            }
        }


    }

    public void sendDependencyMessage() throws Exception{
        logger.info("属地第三方行销即时激励每天上午9点向当前处理人发送一次提醒短信，发送时间截止每月9号");
        SPrivData sPrivData = new SPrivData();
        List<DependencyBusiThirdXXJSJL> busiThirdXXJSJLS = new ArrayList<DependencyBusiThirdXXJSJL>();
        AgentAdjustUse adjustUse = new AgentAdjustUse();
        List<AgentAdjustUse> agentAdjustUses = new ArrayList<AgentAdjustUse>();
        Date date = new Date();
        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMM");
        Calendar cal=Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH,-1);
        String billMonth=sdf.format(cal.getTime());
        DependencyBusiThirdXXJSJL dependencyBusiThirdXXJSJL = new DependencyBusiThirdXXJSJL();
        dependencyBusiThirdXXJSJL.setBillMonth(billMonth);
        logger.info("开始查询T-1月录入的属地第三方行销即时激励数据！！！");
        List<DependencyBusiThirdXXJSJL> dependencyBusiThirdXXJSJLList = dependencyBusiThirdXXJSJLDao.query(dependencyBusiThirdXXJSJL);
        List<Long> doneCodes = null;
        if (dependencyBusiThirdXXJSJLList.size()>0){
            //遍历筛选 待审批数据 一个序列号 只发送一次
            for (DependencyBusiThirdXXJSJL entity:dependencyBusiThirdXXJSJLList) {
                if (!doneCodes.contains(entity.getDoneCode())){
                    if (entity.getAuditStatus() == 0 || entity.getAuditStatus() == 1){
                        busiThirdXXJSJLS.add(entity);
                        doneCodes.add(entity.getDoneCode());
                    }
                }
            }
        }
        if (busiThirdXXJSJLS.size()>0){
            logger.info("------------属地第三方行销即时激励开始待审批数据短信提醒！！！------------");
            for (DependencyBusiThirdXXJSJL entity: busiThirdXXJSJLS) {
                if (entity.getAgentAdjustUseId() != null){
                    adjustUse.setAgentAdjustUseId(entity.getAgentAdjustUseId());
                    agentAdjustUses = agentAdjustUseDao.query(adjustUse);
                    if (agentAdjustUses.size()>0){
                        try{
                            //开始定义短信信息，并调接口发送至审批人手机
                            String name =entity.getOrgName() + "-" + entity.getOpName() + "-属地第三方行销即时激励审批-" +
                                    DateUtil.formatDate(new Date(),"yyyyMMdd") + "-" + entity.getDoneCode();
                            String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为"+name+"";
                            logger.info(messager1);
                            String smsCode = "100011051243";
                            SPrivData sPrivData1 = sPrivData;
                            sPrivData1.setOpId(999990131L);
                            sPrivData1.setOrgId(0L);
                            channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);

                        }catch (Exception e){
                            logger.error("发送短信给审批人异常，失败原因是：" + e);
                        }
                    }
                }
            }
        }else{
            logger.info("--------------属地第三方行销即时激励没有待审批的数据---------------");
        }

    }

    @Override
    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRES_NEW)
    public List<List<Object>> midHighEndRetentionAssessmentAdd(HttpServletRequest request, Long agentAdjustUseId,
                                                               String remoteFileName, String remotePath,
                                                               SPrivData sPrivData) throws Exception {
        logger.info("开始处理中高端保有项目人员月度考评录入文件");

        List<List<Object>> lists = new ArrayList<List<Object>>();
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multipartRequest.getFile("files");

        if (file == null || file.isEmpty()) {
            throw new Exception("上传文件为空！");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            throw new Exception("文件格式不正确，请上传Excel文件！");
        }

        InputStream inputStream = file.getInputStream();
        Workbook workbook = null;

        try {
            if (fileName.endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(inputStream);
            } else {
                workbook = new HSSFWorkbook(inputStream);
            }

            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                throw new Exception("Excel文件中没有找到工作表！");
            }

            // 获取当前月份
            String currentMonth = DateUtil.formatCurrentDate("yyyyMM");
            Integer billMonth = Integer.parseInt(currentMonth);

            // 清理当月已有数据（覆盖上传）
            MidHighEndRetentionAssessment deleteEntity = new MidHighEndRetentionAssessment();
            deleteEntity.setBillMonth(billMonth);
            deleteEntity.setOrgId(sPrivData.getOrgId());
            deleteEntity.setRecStatus(-1);
            midHighEndRetentionAssessmentDao.update(deleteEntity);

            int rowCount = 0;
            int successCount = 0;
            int errorCount = 0;

            // 从第2行开始读取数据（第1行为标题）
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                rowCount++;
                List<Object> rowData = new ArrayList<Object>();

                try {
                    // 读取Excel数据
                    String roleName = getCellStringValue(row.getCell(0)); // 角色
                    String ebcJobNumber = getCellStringValue(row.getCell(1)); // EBC工号名称
                    Double assessmentCoefficient = getCellDoubleValue(row.getCell(2)); // 当月考核系数
                    Double basicCostAdjustFee = getCellDoubleValue(row.getCell(3)); // 基本费用调整费
                    Double adjustFee = getCellDoubleValue(row.getCell(4)); // 调整费

                    // 数据验证
                    validateMidHighEndRetentionData(roleName, ebcJobNumber, assessmentCoefficient,
                                                   basicCostAdjustFee, adjustFee, i + 1);

                    // 创建实体对象
                    MidHighEndRetentionAssessment assessment = new MidHighEndRetentionAssessment();
                    assessment.setBillMonth(billMonth);
                    assessment.setRoleName("中高端保有"); // 固定值
                    assessment.setEbcJobNumber(ebcJobNumber);
                    assessment.setAssessmentCoefficient(assessmentCoefficient);
                    assessment.setBasicCostAdjustFee(basicCostAdjustFee);
                    assessment.setAdjustFee(adjustFee);
                    assessment.setOrgId(sPrivData.getOrgId());
                    assessment.setOrgName(sPrivData.getOrgName());
                    assessment.setOpId(sPrivData.getOpId());
                    assessment.setUserName(sPrivData.getUserName());
                    assessment.setRecStatus(0); // 待审核
                    assessment.setAgentAdjustUseId(agentAdjustUseId);
                    assessment.setFileName(remoteFileName);
                    assessment.setFilePath(remotePath);

                    // 批量添加
                    addMidHighEndRetentionAssessmentForBatch(assessment);

                    successCount++;
                    rowData.add("第" + (i + 1) + "行");
                    rowData.add("成功");
                    rowData.add("");

                } catch (Exception e) {
                    errorCount++;
                    rowData.add("第" + (i + 1) + "行");
                    rowData.add("失败");
                    rowData.add(e.getMessage());
                    logger.error("处理第" + (i + 1) + "行数据失败：" + e.getMessage(), e);
                }

                lists.add(rowData);
            }

            logger.info("中高端保有项目人员月度考评录入完成，总行数：{}，成功：{}，失败：{}",
                       rowCount, successCount, errorCount);

        } finally {
            if (workbook != null) {
                workbook.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
        }

        return lists;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRES_NEW)
    public void addMidHighEndRetentionAssessmentForBatch(MidHighEndRetentionAssessment assessment) throws Exception {
        try {
            // 获取序列号
            Long doneCode = midHighEndRetentionAssessmentDao.getSequence();
            assessment.setDoneCode(doneCode);
            assessment.setDoneDate(new Date());

            // 插入数据
            midHighEndRetentionAssessmentDao.insert(assessment);

            // 发送审批短信
            sendApprovalSMS(assessment);

        } catch (Exception e) {
            logger.error("批量添加中高端保有项目人员月度考评信息失败", e);
            throw new Exception("保存数据失败：" + e.getMessage());
        }
    }
