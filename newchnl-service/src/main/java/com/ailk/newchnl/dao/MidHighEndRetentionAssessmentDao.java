/**
 * $Id: MidHighEndRetentionAssessmentDao.java,v 1.0 2025/09/04 $
 * <p>
 * Copyright 2016 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.dao;

import com.ailk.newchnl.dao.impl.BaseDao;
import com.ailk.newchnl.entity.MidHighEndRetentionAssessment;
import com.ailk.newchnl.mybatis.pagination.PageParameter;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 中高端保有项目人员月度考评DAO接口
 * <AUTHOR> @version $Id: MidHighEndRetentionAssessmentDao.java,v 1.0 2025/09/04 $
 * Created on 2025/09/04
 */
@Repository("midHighEndRetentionAssessmentDao")
public interface MidHighEndRetentionAssessmentDao extends BaseDao<MidHighEndRetentionAssessment> {

    /**
     * 查询中高端保有项目人员月度考评信息
     * @param entity 查询条件
     * @param page 分页参数
     * @return 查询结果列表
     */
    public List<MidHighEndRetentionAssessment> queryMidHighEndRetentionAssessmentInfo(
            @Param(value = "entity") MidHighEndRetentionAssessment entity,
            @Param(value = "page") PageParameter page);
}