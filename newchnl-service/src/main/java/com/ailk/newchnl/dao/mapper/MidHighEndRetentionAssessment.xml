<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ailk.newchnl.dao.MidHighEndRetentionAssessmentDao">
    <select id="getSequence" resultType="long">
        select MID_HIGH_END_RETENTION_SEQ.nextval from dual
    </select>
    
    <select id="queryMidHighEndRetentionAssessmentInfo" parameterType="map" resultType="com.ailk.newchnl.entity.MidHighEndRetentionAssessment">
        select a.*
        from mid_high_end_retention_assessment a
        where a.rec_status != -1
        <if test="entity.recStatusList != null and entity.recStatusList != ''">
            and rec_status in
            <foreach collection="entity.recStatusList.split(',')" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <choose>
            <when test="entity.billMonth != null">
                AND a.bill_month = #{entity.billMonth}
            </when>
        </choose>
        <choose>
            <when test="entity.recStatus != null">
                AND a.rec_status = #{entity.recStatus}
            </when>
        </choose>
        <choose>
            <when test="entity.orgId != null">
                AND a.org_id = #{entity.orgId}
            </when>
        </choose>
        <choose>
            <when test="entity.agentAdjustUseId != null">
                AND a.agent_adjust_use_id = #{entity.agentAdjustUseId}
            </when>
        </choose>
    </select>

    <insert id="insert" parameterType="map">
        INSERT INTO mid_high_end_retention_assessment (
            done_code,
            bill_month,
            ebc_job_number,
            assessment_coefficient,
            basic_cost_adjust_fee,
            adjust_fee,
            org_id,
            org_name,
            op_id,
            user_name,
            done_date,
            rec_status,
            agent_adjust_use_id,
            rec_status_approve,
            file_name,
            file_path,
            role_name)
        VALUES
            (#{doneCode,jdbcType=NUMERIC},
             #{billMonth,jdbcType=NUMERIC},
             #{ebcJobNumber,jdbcType=VARCHAR},
             #{assessmentCoefficient,jdbcType=NUMERIC},
             #{basicCostAdjustFee,jdbcType=NUMERIC},
             #{adjustFee,jdbcType=NUMERIC},
             #{orgId,jdbcType=NUMERIC},
             #{orgName,jdbcType=VARCHAR},
             #{opId,jdbcType=NUMERIC},
             #{userName,jdbcType=VARCHAR},
             #{doneDate,jdbcType=TIMESTAMP},
             #{recStatus,jdbcType=NUMERIC},
             #{agentAdjustUseId,jdbcType=NUMERIC},
             #{recStatusApprove,jdbcType=VARCHAR},
             #{fileName,jdbcType=VARCHAR},
             #{filePath,jdbcType=VARCHAR},
             #{roleName,jdbcType=VARCHAR})
    </insert>

    <select id="query" parameterType="map" resultType="com.ailk.newchnl.entity.MidHighEndRetentionAssessment">
        select a.*
        from mid_high_end_retention_assessment a
        where a.rec_status = 1
        <choose>
            <when test="entity.billMonth != null">
                AND a.bill_month = #{entity.billMonth}
            </when>
        </choose>
    </select>

    <update id="update" parameterType="map">
        UPDATE mid_high_end_retention_assessment
        <set>
            <if test="doneDate != null">DONE_DATE = #{doneDate},</if>
            <if test="recStatus != null">REC_STATUS = #{recStatus},</if>
            <if test="agentAdjustUseId != null">agent_adjust_use_id = #{agentAdjustUseId},</if>
            <if test="recStatusApprove != null">rec_status_approve = #{recStatusApprove},</if>
        </set>
        WHERE  1=1
        <choose>
            <when test="doneCode != null and doneCode != ''">
                and done_code = #{doneCode}
            </when>
        </choose>
        <choose>
            <when test="orgId != null and orgId != ''">
                and org_id = #{orgId}
            </when>
        </choose>
        <choose>
            <when test="billMonth != null and billMonth != ''">
                and bill_month = #{billMonth}
            </when>
        </choose>
    </update>
</mapper>