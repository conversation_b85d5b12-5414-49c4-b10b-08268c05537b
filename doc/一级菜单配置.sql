--���ݿ�  �и�����Ȩ��  
sec/Csxt-123@shpubbcv1 
--ִ������insert ���Ȳ�ѯ�Ƿ��Ѿ�����
--��ѯ��
SELECT * FROM sec_domain t where t.domain_id=3;
--���ܼ�
SELECT * FROM sec_role t WHERE t.role_id=40000009;
--�󶨹��ܼ��˵�
SELECT * FROM sec_role_grant t where t.role_id=40000009;
--��ѯһ���˵��Ƿ��Ѿ�������ͬ��func_id
SELECT * FROM sec_function t where t.func_id IN(1301,1302,1303,1304,1305,1306,1307,1308,1309);
--�����̶��������˵� 30101 ����
SELECT * FROM sec_function t where t.func_id IN(30101,3010101,3010102,3010103,3010104,3010105,3010106);
--���� һ���������˵�
SELECT * FROM sec_function t where t.func_id IN(30102,3010201,3010202,3010203,3010204,3010205,3010206);
--ʵ���Ӧ��ϵ��������
SELECT * FROM sec_function t where t.func_id IN(30103,3010301,3010302);


--��һ����
insert into sec.sec_role(role_id,role_name,role_type,domain_id,state,create_date,done_date,done_code,op_id,org_id)
values(40000009,'������ȫ�����ܼ�',16,3,1,sysdate,sysdate,0,0,0);



-----�������������������˵����õ����ܼ�����ȥ
insert into sec.sec_role_grant(role_grant_id,role_id,priv_id,ent_id,ent_type,notes,state,ent_valid_date,ent_expire_date)
select sec.sec_role_grant$seq.nextval,role_id,1,func_id,'F','new_func',1,sysdate,sysdate+interval '86' year from 
sec.sec_role a,(select func_id  from sec.sec_function t where t.domain_id = 3) b
where a.role_id in (40000009);


--�ڶ�����һ��Ŀ¼
insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 1301,1,'������',3,'������',-1,1,1,'','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 1302,1,'ҵ��',3,'ҵ��',-1,1,1,'','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 1303,1,'��Դ',3,'��Դ',-1,1,1,'','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 1304,1,'Ӫ���',3,'Ӫ���',-1,1,1,'','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 1305,1,'���',3,'���',-1,1,1,'','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 1306,1,'����',3,'����',-1,1,1,'','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 1307,1,'ϵͳ',3,'ϵͳ',-1,1,1,'','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 1308,1,'��������',3,'��������',-1,1,1,'','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 1309,1,'��Ա����',3,'��Ա����',-1,1,1,'','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

--��������Ϣ����
--����Ŀ¼
insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 30101,1,'��������Ϣ����',3,'��������Ϣ����',1301,2,1,'','H','00000000',1,1,999999999999,sysdate,sysdate from dual;
--����Ŀ¼
insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010101,1,'�������������',3,'�������������',30101,3,1,'agent/agentImportManager','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010102,1,'��������Ϣ��ѯ',3,'��������Ϣ��ѯ',30101,3,1,'agent/agentInfoQuery','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010103,1,'��������Ϣ�޸�',3,'��������Ϣ�޸�',30101,3,1,'agent/agentInfoModify','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010104,1,'��������Ϣ����',3,'��������Ϣ����',30101,3,1,'agent/agentInfoCheack','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010105,1,'������Ԥ����',3,'������Ԥ����',30101,3,1,'agent/agentPreAway','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010106,1,'����������',3,'����������',30101,3,1,'agent/agentAway','H','00000000',1,1,999999999999,sysdate,sysdate from dual;


--������Ϣ����
--����Ŀ¼
insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 30102,1,'������Ϣ����',3,'������Ϣ����',1301,2,1,'','H','00000000',1,1,999999999999,sysdate,sysdate from dual;
--����Ŀ¼ 
insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010201,1,'������Ϣ��ѯ',3,'������Ϣ��ѯ',30102,3,1,'node/showPage?codeId=99'||chr(38)||'channelEntityId='||chr(38)||'operateType=','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010202,1,'������Ϣ¼��',3,'������Ϣ¼��',30102,3,1,'node/showPage?codeId=100'||chr(38)||'channelEntityId='||chr(38)||'operateType=','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010203,1,'��Ȧ��Ϣ����',3,'��Ȧ��Ϣ����',30102,3,1,'commerceCircle/list','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010204,1,'���еع���',3,'���еع���',30102,3,1,'channelCentrally/channelCentrally','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010205,1,'֧���������',3,'֧���������',30102,3,1,'channelSupportOffce/channelSupportOffce','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010206,1,'֧���ֻ���������',3,'֧���ֻ���������',30102,3,1,'channelSupportPhoneStoreController/channelSupportPhoneStoreController','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

--ʵ���Ӧ��ϵ����
--����Ŀ¼
insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 30103,1,'ʵ���Ӧ��ϵ����',3,'ʵ���Ӧ��ϵ����',1301,2,1,'','H','00000000',1,1,999999999999,sysdate,sysdate from dual;
--����Ŀ¼
insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010301,1,'ʵ���Ӧ��ϵ',3,'ʵ���Ӧ��ϵ',30103,3,1,'channelEntityMatchOrg/channelEntityMatchOrg','H','00000000',1,1,999999999999,sysdate,sysdate from dual;

insert into sec.sec_function(func_id,ent_class_id,name,domain_id,notes,parent_id,func_level,fun_seq,viewname,func_type,verify_mode,module_type,state,done_code,create_date,done_date)
select 3010302,1,'����ʵ���Ӧ��ϵ',3,'����ʵ���Ӧ��ϵ',30103,3,1,'channelEntityMatchOrg/entityMatchOrgBatch','H','00000000',1,1,999999999999,sysdate,sysdate from dual;


