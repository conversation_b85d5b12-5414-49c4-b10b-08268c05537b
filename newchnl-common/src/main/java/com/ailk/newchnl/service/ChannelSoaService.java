/*
 * $Id: ChannelSoaService.java,v 1.2 2014/10/16 01:59:37 fuqiang Exp $
 *
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.service;

import java.util.Map;

import com.ailk.newchnl.entity.SPrivData;

/**
 * <AUTHOR>
 * @version $Id: ChannelSoaService.java,v 1.2 2014/10/16 01:59:37 fuqiang Exp $
 * Created on 2014年8月8日 下午1:12:08
 */
public interface ChannelSoaService {

	/**
	 * 已废弃, 调统一接口.仅返回报文中的RetInfo信息
	 * @param functionName  对应channel_soa_client_config表中的FUNCTION_NAME
	 * @param busiParams 对于具体的接口参数
	 * @param sPrivData 人员信息
	 * @return 调用结果
	 */
    @Deprecated
	public String call(String functionName, Map<String, Object> busiParams, SPrivData sPrivData) throws Exception;


    /**
     * 调统一接口. 返回报文的全部信息
     * @param functionName
     * @param busiParams
     * @param sPrivData
     * @return
     * @throws Exception
     */
    public String call2(String functionName, Map<String, Object> busiParams, SPrivData sPrivData) throws Exception;

	/**
	 * 发起GET请求. 返回报文的全部信息
	 * @param functionName
	 * @param busiParams
	 * @param sPrivData
	 * @return
	 * @throws Exception
	 */
	public String call3(String functionName, Map<String, Object> busiParams, SPrivData sPrivData) throws Exception;

	/**
	 * 发起POST请求
	 * @param functionName
	 * @param Param
	 * @param sPrivData
	 * @return
	 * @throws Exception
	 */
	public String call4(String functionName, String Param, SPrivData sPrivData) throws Exception;
	/**
	 * 同步营业厅时间给云店
	 */
	public String call5(String functionName, String Param) throws Exception;

}
