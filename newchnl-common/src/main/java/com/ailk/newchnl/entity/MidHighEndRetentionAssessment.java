package com.ailk.newchnl.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 中高端保有项目人员月度考评实体类
 * 
 * 业务规则说明：
 * 1. 当月考核系数：只允许在0.80至1.20区间中填写，小数点保留两位
 * 2. 当月系统录入分数为上月业务量的分数
 * 3. 基本费用调整费：允许录入正负数，区公司职能自行把控。如无调整填写0
 * 4. 调整费：用于回调前期费用，对数值正负不做限制。如无调整填写0
 * 5. 表格中均为必填选项
 * 6. 录入对象均为中高端保有选项，无其它选项
 * 7. EBC工号由各区公司录入，由于非渠道二期中的信息，暂不需系统校验
 * 8. 覆盖上传：在录入节点允许区公司多次上传，以便校正，以当月最后次上传情况为最终依据
 * 
 * <AUTHOR> @version $Id: MidHighEndRetentionAssessment.java,v 1.0 2025/09/04 $
 * Created on 2025/09/04
 */
public class MidHighEndRetentionAssessment implements Serializable {
    private Long doneCode;                  //序列号
    private Integer billMonth;              //录入月份
    private String roleName;   //角色名称，默认为中高端保有
    private String ebcJobNumber;            //EBC工号名称
    private Double assessmentCoefficient;   //当月考核系数【0.80-1.20】，保留两位小数
    private Double basicCostAdjustFee;      //基本费用调整费（可正负）
    private Double adjustFee;               //调整费（可正负）
    private Long orgId;                     //归属组织
    private String orgName;                 //所属分公司
    private Long opId;                      //操作员Id
    private String userName;                //操作员工号
    private Date doneDate;                  //操作时间
    private Integer recStatus;              //状态 0：待审核  1：审核通过  2：审核驳回   3：-1  失效
    private String recStatusList;           // 用来存放查询条件的字段，数据库中没有
    private Long agentAdjustUseId;          //审批人Id
    private String recStatusApprove;        //审批状态
    private String fileName;                //附件名称
    private String filePath;                //附件下载路径

    public Long getDoneCode() {
        return doneCode;
    }

    public void setDoneCode(Long doneCode) {
        this.doneCode = doneCode;
    }

    public Integer getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(Integer billMonth) {
        this.billMonth = billMonth;
    }

    public String getEbcJobNumber() {
        return ebcJobNumber;
    }

    public void setEbcJobNumber(String ebcJobNumber) {
        this.ebcJobNumber = ebcJobNumber;
    }

    public Double getAssessmentCoefficient() {
        return assessmentCoefficient;
    }

    public void setAssessmentCoefficient(Double assessmentCoefficient) {
        this.assessmentCoefficient = assessmentCoefficient;
    }

    public Double getBasicCostAdjustFee() {
        return basicCostAdjustFee;
    }

    public void setBasicCostAdjustFee(Double basicCostAdjustFee) {
        this.basicCostAdjustFee = basicCostAdjustFee;
    }

    public Double getAdjustFee() {
        return adjustFee;
    }

    public void setAdjustFee(Double adjustFee) {
        this.adjustFee = adjustFee;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getDoneDate() {
        return doneDate;
    }

    public void setDoneDate(Date doneDate) {
        this.doneDate = doneDate;
    }

    public Integer getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(Integer recStatus) {
        this.recStatus = recStatus;
    }

    public String getRecStatusList() {
        return recStatusList;
    }

    public void setRecStatusList(String recStatusList) {
        this.recStatusList = recStatusList;
    }

    public Long getAgentAdjustUseId() {
        return agentAdjustUseId;
    }

    public void setAgentAdjustUseId(Long agentAdjustUseId) {
        this.agentAdjustUseId = agentAdjustUseId;
    }

    public String getRecStatusApprove() {
        return recStatusApprove;
    }

    public void setRecStatusApprove(String recStatusApprove) {
        this.recStatusApprove = recStatusApprove;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }
}