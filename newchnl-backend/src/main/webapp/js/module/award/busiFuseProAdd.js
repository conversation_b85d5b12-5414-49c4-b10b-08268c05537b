/**
 * <AUTHOR>
 * Created on 2023-12-28 09:42:15
 * Copyright 2016 Asiainfo Technologies(China),Inc. All rights reserved.
 */

define(['common/organizationTree', 'common/util','common/accMath'], function (require) {
    var accMath = require('common/accMath');
    var Util = require('common/util');

    function busiFuseProAdd(config) {
        busiFuseProAdd.superclass.constructor.call(this, config);
        this.init();
    }

    busiFuseProAdd.ATTRS = {
        OrganizeTree: {},
        channelEntityType: {},
        districtId: {}
    };
    BUI.extend(busiFuseProAdd, BUI.Base);
    BUI.augment(busiFuseProAdd, {
        init: function () {

            // layout
            var myLayout = new dhtmlXLayoutObject({
                parent: document.body,
                pattern: "1C",
                offsets: {
                    top: 5,
                    right: 5,
                    bottom: 5,
                    left: 5
                },
                cells: [{
                    id: "a",
                    header: false
                }]
            });


            // Form
            var formStructure = [{
                type: "settings",
                position: "label-left",
                labelAlign: "right"
            }, {
                type: "fieldset",
                label: "融合业务零产能批量录入",
                offsetLeft: 20,
                offsetTop: 20,
                width: _width - 360,
                list: [{
                    type: "block",
                    name: "row1"
                }, {
                    type: "block",
                    name: "row2"
                }]
            }];
            var tableWidth = document.documentElement.clientWidth * 4 / 5;
            var myForm = myLayout.cells("a").attachForm(formStructure);
            this.set("myForm", myForm);
            this.set("myLayout", myLayout);
            myForm.enableLiveValidation(true);


            myForm.addItem("row1", {
                type: "file",
                name: "files",
                id: "files",
                label: "导入文件:",
                inputWidth: 200,
                required: true
            }, 0);

            myForm.addItem("row2", {
                type: "button", name: "moduleDownload", value: "模板下载"
            }, 0);
            myForm.addItem("row2", {type: "newcolumn"}, 1);
            myForm.addItem("row2", {
                type: "button", name: "batchInput", value: "批量导入"
            }, 2);
            myForm.addItem("row2", {type: "newcolumn"}, 3);
            myForm.addItem("row2", {type: "button", name: "resultDownload", value: "导入结果下载"}, 4);

            var _util = new Util().dhtmlxProcessOn("正在加载。。。");
            $.post(BUI.ctx + '/agentAward/getOrganizationId', function (data) {
                new Util().dhtmlxProcessOff(_util);
                if (data.orgId == 6){
                    myForm.disableItem("batchInput");
                    myForm.disableItem("resultDownload");
                    myForm.disableItem("delBtn");
                }

            });

            this.initDomEvent();
        },
        initDomEvent: function () {
            var _this = this;
            var myForm = _this.get("myForm");

            myForm.attachEvent("onButtonClick", function (name) {
                if (name == "batchInput") {
                    var files = myForm.getItemValue("files");
                    if (files == "" || files == null) {
                        dhtmlx.alert({
                            title: "提示", text: "请选择要导入的文件!", type: "alert-warning"
                        });
                        return;
                    }
                    var _util = new Util().dhtmlxProcessOn("批量导入中...");

                    $.ajaxFileUpload({
                        type: "POST",
                        url: BUI.ctx + '/agentAward/busiFuseProInfoBatchAdd.html?fileName=' + encodeURI(encodeURI(files)) ,
                        secureuri: false,  //是否启用安全提交，默认为false
                        fileElementId: $("input[name=files]")[0].id,//'files', //需要上传的文件域的ID，即<input type="file">的ID
                        dataType: 'JSON', //服务器返回的数据类型。可以为xml,script,json,html。如果不填写，jQuery会自动判断。 注意使用大写
                        success: function (data) {
                            new Util().dhtmlxProcessOff(_util);
                            if (data.status == "0") {
                                dhtmlx.alert({
                                    title: "提示", text: "操作成功，请通过导入结果下载按钮下载导入结果！"
                                });
                            } else {
                                dhtmlx.alert({
                                    title: "提示", text: data.message, type: "alert-error"
                                });
                            }
                        },
                        error: function (status, msg) {
                            dhtmlx.alert({
                                title: "提示", text: "处理异常！", type: "alert-error"
                            });
                            new Util().dhtmlxProcessOff(_util);
                        }
                    });
                } else if (name == "resultDownload") {
                    window.location.href = BUI.ctx + "/agentAward/busiFuseProInfoBatchAddResultExport";
                } else if (name == "moduleDownload") {
                    var util = new Util();
                    util.download("/templet/busiFuseProAdd.xlsx", "busiFuseProAdd.xlsx", false);
                }
            });

        }
    });

    return busiFuseProAdd;
});
