/**
 * <AUTHOR>
 * Created on 2024/10/18 11:57
 * Copyright 2016 Asiainfo Technologies(China),Inc. All rights reserved.
 */

define(['common/organizationTree', 'common/util'], function (require) {
    var Util = require('common/util');

    function busiThirdSetDateAdd(config) {
        busiThirdSetDateAdd.superclass.constructor.call(this, config);
        this.init();
    }

    busiThirdSetDateAdd.ATTRS = {
        OrganizeTree: {},
        channelEntityType: {},
        districtId: {}
    };
    BUI.extend(busiThirdSetDateAdd, BUI.Base);
    BUI.augment(busiThirdSetDateAdd, {
        init: function () {

            // layout
            var myLayout = new dhtmlXLayoutObject({
                parent: document.body,
                pattern: "1C",
                offsets: {
                    top: 5,
                    right: 5,
                    bottom: 5,
                    left: 5
                },
                cells: [{
                    id: "a",
                    header: false
                }]
            });


            // Form
            var formStructure = [{
                type: "settings",
                position: "label-left",
                labelWidth: "280",
                labelAlign: "center"
            }, {
                type: "fieldset",
                label: "第三方直销反向折算机制时间设置录入",
                offsetLeft: 10,
                offsetTop: 10,
                list: [{
                    type: "block",
                    name: "row1"
                }, {
                    type: "block",
                    name: "row2"
                }, {
                    type: "block",
                    name: "row3"
                }, {
                    type: "block",
                    name: "row4"
                }, {
                    type: "block",
                    name: "row5"
                }, {
                    type: "block",
                    name: "row6"
                }]
            }];

            var myForm = myLayout.cells("a").attachForm(formStructure);
            this.set("myForm", myForm);

            myForm.addItem("row1", {
                type: "calendar",
                name: 'billMonth',
                dateFormat: "%Y%m",
                label: '目标月份：',
                width: 200
            }, 0);

            myForm.addItem("row2", {
                type: "calendar",
                name: 'startDate',
                dateFormat: "%Y%m%d",
                label: '开始时间：',
                width: 200
            }, 0);
            myForm.addItem("row3", {
                type: "calendar",
                name: 'endDate',
                dateFormat: "%Y%m%d",
                label: '结束时间：',
                width: 200
            }, 0);


            myForm.addItem("row5",{type:"button",name : "save",value : "保存",offsetLeft : 200,offsetTop : 5}, 0);
            var date = new Date();
            // date.setMonth(date.getMonth()-4);
            var y = date.getFullYear();
            var m = date.getMonth() +1;
            var d = date.getDate();
            m = m < 10 ? '0' + m : m;
            myForm.setItemValue("billMonth", y + "" + m);
            myForm.setItemValue("startDate", y+ "" + m + "" +d);
            myForm.setItemValue("endDate", y + "" + m + "" + d);

            this.initDomEvent();
        },
        initDomEvent: function () {
            var _this = this;
            var myForm = _this.get("myForm");

            myForm.attachEvent("onButtonClick", function (name) {
                if (name == "save") {
                    var billMonth = myForm.getCalendar("billMonth").getFormatedDate("%Y%m");
                    var startMonth = myForm.getCalendar("startDate").getFormatedDate("%Y%m");
                    var endMonth = myForm.getCalendar("endDate").getFormatedDate("%Y%m");
                    var startDate = myForm.getCalendar("startDate").getFormatedDate("%Y%m%d");
                    var endDate = myForm.getCalendar("endDate").getFormatedDate("%Y%m%d");
                    // 当前时间
                    var date = new Date();
                    var nowYear = date.getFullYear();
                    var nowMonth = date.getMonth()+1;
                    nowMonth = nowMonth < 10 ? '0' + nowMonth : nowMonth;
                    var nowDate = nowYear  + '' + nowMonth;
                    var nowDate = nowYear  + '' + nowMonth;

                    var billDate = new Date(myForm.getCalendar("billMonth").getFormatedDate("%Y-%m"));

                    //录入最小时间
                    billDate.setMonth(billDate.getMonth()+1);
                    var minYear = billDate.getFullYear();
                    var minMonth = billDate.getMonth()+1;
                    minMonth = minMonth < 10 ? '0' + minMonth : minMonth;
                    var minDate = minYear + '' + minMonth;

                    //录入最大时间
                    billDate.setMonth(billDate.getMonth()+3);
                    var maxYear = billDate.getFullYear();
                    var maxMonth = billDate.getMonth()+1;
                    maxMonth = maxMonth < 10 ? '0' + maxMonth : maxMonth;
                    var maxDate = maxYear + '' +  maxMonth
                    if (billMonth != startMonth || billMonth != endMonth){
                        dhtmlx.alert({
                            title: "错误", text: "目标月份与开始的时间月份和结束的月份必须保持一致，请重新输入！！！", type: "alert-error"
                        });
                        return;
                    }
                    if (startDate>endDate){
                        dhtmlx.alert({
                            title: "错误", text: "开始时间不能大于结束时间，请重新输入！！！", type: "alert-error"
                        });
                        return;
                    }


                    dhtmlx.message({
                        type: "confirm-warning",
                        text: "您确定要录入第三方直销反向折算机制时间设置的信息吗？",
                        ok: "确定",
                        cancel: "取消",
                        callback: function (a) {
                            if (a) {
                                var _util = new Util().dhtmlxProcessOn("正在操作。。。");
                                var param = {
                                    billMonth: billMonth,
                                    startDate: startDate,
                                    endDate: endDate
                                };
                                $.post(BUI.ctx + '/agentAward/busiThirdSetDateInfoAdd', param, function (data) {
                                    new Util().dhtmlxProcessOff(_util);
                                    if ("1" == data.status) {
                                        dhtmlx.alert({title: "错误", text: data.message, type: "alert-error"});
                                    } else if ("0" == data.status) {
                                        dhtmlx.alert({
                                            title: '消息', text: data.message, callback: function () {
                                                window.location.reload();
                                            }
                                        });
                                    }
                                });

                            }
                        }
                    });
                }
            });


            myForm.attachEvent("onChange", function (name, value) {

            });
        }
    });

    return busiThirdSetDateAdd;
});