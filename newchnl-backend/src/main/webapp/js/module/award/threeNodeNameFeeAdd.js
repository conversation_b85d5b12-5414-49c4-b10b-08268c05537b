/**
 * 属地第三方直销费用调整录入
 * <AUTHOR>
 * Created on 2023-6-28 09:42:15
 * Copyright 2016 Asiainfo Technologies(China),Inc. All rights reserved.
 */

define(['common/organizationTree', 'common/util','common/accMath'], function (require) {
    var accMath = require('common/accMath');
    var Util = require('common/util');

    function threeNodeNameFeeAdd(config) {
        threeNodeNameFeeAdd.superclass.constructor.call(this, config);
        this.init();
    }

    threeNodeNameFeeAdd.ATTRS = {
        OrganizeTree: {},
        channelEntityType: {},
        districtId: {}
    };
    BUI.extend(threeNodeNameFeeAdd, BUI.Base);
    BUI.augment(threeNodeNameFeeAdd, {
        init: function () {

            // layout
            var myLayout = new dhtmlXLayoutObject({
                parent: document.body,
                pattern: "1C",
                offsets: {
                    top: 5,
                    right: 5,
                    bottom: 5,
                    left: 5
                },
                cells: [{
                    id: "a",
                    header: false
                }]
            });


            // Form
            var formStructure = [{
                type: "settings",
                position: "label-left",
                labelAlign: "right"
            }, {
                type: "fieldset",
                label: "属地第三方直销费用调整录入",
                offsetLeft: 20,
                offsetTop: 20,
                width: _width - 360,
                list: [{
                    type: "block",
                    name: "row1"
                },{
                    type: "block",
                    name: "row3"
                }, {
                    type: "block",
                    name: "row2"
                }]
            }];
            var tableWidth = document.documentElement.clientWidth * 4 / 5;
            var myForm = myLayout.cells("a").attachForm(formStructure);
            this.set("myForm", myForm);
            this.set("myLayout", myLayout);
            myForm.enableLiveValidation(true);


            myForm.addItem("row1", {
                type: "file",
                name: "files",
                id: "files",
                label: "导入文件:",
                inputWidth: 200,
                required: true
            }, 0);
            myForm.addItem("row1", {type: "newcolumn"}, 1);
            myForm.addItem("row1",{
                type: "file",
                name: "filesUp",
                id: "filesUp",
                label: "附件上传:",
                inputWidth: 200,
                offsetLeft:135,
                required: true
            }, 2);

            myForm.addItem("row2", {
                type: "button", name: "moduleDownload", value: "模板下载"
            }, 0);
            myForm.addItem("row2", {type: "newcolumn"}, 1);
            myForm.addItem("row2", {
                type: "button", name: "batchInput", value: "批量导入"
            }, 2);
            myForm.addItem("row2", {type: "newcolumn"}, 3);
            myForm.addItem("row2", {type: "button", name: "resultDownload", value: "导入结果下载"}, 4);

            myForm.addItem("row3", {type: "combo",  name : "flowAjudt", label: "审批流程：",
                width: 100, readonly: true,required: true, validate: "NotEmpty"},0);
            myForm.addItem("row3", {type: "newcolumn"}, 1);
            myForm.addItem("row3", {type: "combo",  name : "flowThreeAmader", label: "三级经理：",
                offsetLeft:59, width: 150, readonly: true,required: true, validate: "NotEmpty"}, 2);
            myForm.addItem("row3", {type: "newcolumn"}, 3);
            myForm.addItem("row3" ,{type: "combo",  name : "flowThreeAmaderName", label: "三级经理审批人：",
                offsetLeft:59, width: 100, readonly: true,required: true, validate: "NotEmpty"},4)

            myForm.getCombo("flowAjudt").addOption([["1","属地内部流程"]]);
            myForm.getCombo("flowThreeAmader").addOption([["2","起草人三级经理"]]);

            var _util = new Util().dhtmlxProcessOn("正在加载。。。");
            $.post(BUI.ctx + '/agentAward/getOrganizationId', function (data) {
                new Util().dhtmlxProcessOff(_util);
                if (data.orgId == 6){
                    myForm.disableItem("batchInput");
                    myForm.disableItem("resultDownload");
                    myForm.disableItem("delBtn");
                }

            });

            this.initDomEvent();
        },
        initDomEvent: function () {
            var _this = this;
            var myForm = _this.get("myForm");
            var myGrid = _this.get("mygrid");

            myForm.attachEvent("onButtonClick", function (name) {
                if (name == "batchInput") {
                    var files = myForm.getItemValue("files");
                    var filesUp = myForm.getItemValue("filesUp");
                    var flowAjudt = myForm.getItemValue("flowAjudt");
                    var flowThreeAmader = myForm.getItemValue("flowThreeAmader");
                    var flowThreeAmaderName = myForm.getItemValue("flowThreeAmaderName");
                    var billMonths = new Date().getFullYear() + "" + ( (new Date().getMonth() + 1) < 10 ? ("0" + (new Date().getMonth() + 1)) : (new Date().getMonth()+1));
                    var remoteFileName = "";    //附件上传文件名称
                    var remotePath = "";        //附件上传文件路径
                    if ((flowAjudt == "" || flowAjudt == null) || (flowThreeAmader == "" || flowThreeAmader == null)
                        || (flowThreeAmaderName == "" || flowThreeAmaderName == null) ){
                        dhtmlx.alert({
                            title: "提示", text: "请选择审批人信息!", type: "alert-warning"
                        });
                        return;
                    }
                    if (files == "" || files == null) {
                        dhtmlx.alert({
                            title: "提示", text: "请选择要导入的文件!", type: "alert-warning"
                        });
                        return;
                    }
                    if (filesUp == "" || filesUp == null){
                        dhtmlx.alert({
                            title: "提示", text: "请选择要上传的附件!", type: "alert-warning"
                        });
                        return;
                    }
                    util = new Util().dhtmlxProcessOn("批量导入中...");
                    $.ajaxFileUpload({
                        type: "POST",
                        url: BUI.ctx + '/agentAward/threeNodeFilesUpload.html?billMonth=' + billMonths + '&fileName=' + encodeURI(encodeURI(filesUp)),
                        secureuri: false,  //是否启用安全提交，默认为false
                        fileElementId: $("input[name=filesUp]")[0].id,//'filesUp', //需要上传的文件域的ID，即<input type="filesUp">的ID
                        dataType: 'JSON', //服务器返回的数据类型。可以为xml,script,json,html。如果不填写，jQuery会自动判断。 注意使用大写
                        success: function (data) {
                            if (data.status == "1") {
                                remoteFileName = data.remoteFileName;
                                remotePath = data.remotePath;
                                $.ajaxFileUpload({
                                    type: "POST",
                                    url: BUI.ctx + '/agentAward/threeNodeNameFeeAdd.html?fileName=' + encodeURI(encodeURI(files)) + '&flowThreeAmaderName=' + encodeURI(encodeURI(flowThreeAmaderName))
                                        + '&remoteFileName=' + encodeURI(encodeURI(remoteFileName)) + '&remotePath=' + encodeURI(encodeURI(remotePath)) ,
                                    // url: BUI.ctx + '/agentAward/threeNodeNameFeeAdd.html?fileName=' + encodeURI(encodeURI(files)),
                                    secureuri: false,  //是否启用安全提交，默认为false
                                    fileElementId: $("input[name=files]")[0].id,//'files', //需要上传的文件域的ID，即<input type="file">的ID
                                    dataType: 'JSON', //服务器返回的数据类型。可以为xml,script,json,html。如果不填写，jQuery会自动判断。 注意使用大写
                                    success: function (data) {
                                        new Util().dhtmlxProcessOff(util);
                                        if (data.status == "0") {
                                            dhtmlx.alert({
                                                title: "提示", text: "操作成功，请通过导入结果下载按钮下载导入结果！"
                                            });
                                        } else {
                                            dhtmlx.alert({
                                                title: "提示", text: data.message, type: "alert-error"
                                            });
                                        }
                                    }
                                });
                            }else{
                                dhtmlx.alert({
                                    title: "提示", text: "文件上传失败!", type: "alert-error"
                                });
                                return;
                            }
                        }
                    });
                } else if (name == "resultDownload") {
                    window.location.href = BUI.ctx + "/agentAward/threeNodeNameFeeInfoBatchAddResultExport";
                } else if (name == "moduleDownload") {
                    var util = new Util();
                    util.download("/templet/threeNodeNameFeeAdd.xlsx", "threeNodeNameFeeAdd.xlsx", false);
                }
            });

            myForm.attachEvent("onChange", function (name, value){
                if(name == "flowThreeAmader"){
                    var flowThreeAmader = myForm.getCombo("flowThreeAmader").getSelectedValue();
                    myForm.getCombo("flowThreeAmaderName").load(BUI.ctx + "/agentAdjustFeeCller/queryResTypeByOrgId?resType=" + flowThreeAmader);
                }
            });

        }
    });

    return threeNodeNameFeeAdd;
});
